{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{4FF9D670-250B-444A-933B-2AD1306B6451}|Account Hosting\\Account Hosting.csproj|c:\\users\\<USER>\\source\\repos\\account hosting\\account hosting\\services\\configurationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{4FF9D670-250B-444A-933B-2AD1306B6451}|Account Hosting\\Account Hosting.csproj|solutionrelative:account hosting\\services\\configurationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{4FF9D670-250B-444A-933B-2AD1306B6451}|Account Hosting\\Account Hosting.csproj|c:\\users\\<USER>\\source\\repos\\account hosting\\account hosting\\services\\notificationschedulerservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{4FF9D670-250B-444A-933B-2AD1306B6451}|Account Hosting\\Account Hosting.csproj|solutionrelative:account hosting\\services\\notificationschedulerservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{4FF9D670-250B-444A-933B-2AD1306B6451}|Account Hosting\\Account Hosting.csproj|c:\\users\\<USER>\\source\\repos\\account hosting\\account hosting\\viewmodels\\baseviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{4FF9D670-250B-444A-933B-2AD1306B6451}|Account Hosting\\Account Hosting.csproj|solutionrelative:account hosting\\viewmodels\\baseviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{4FF9D670-250B-444A-933B-2AD1306B6451}|Account Hosting\\Account Hosting.csproj|c:\\users\\<USER>\\source\\repos\\account hosting\\account hosting\\viewmodels\\mainviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{4FF9D670-250B-444A-933B-2AD1306B6451}|Account Hosting\\Account Hosting.csproj|solutionrelative:account hosting\\viewmodels\\mainviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{4FF9D670-250B-444A-933B-2AD1306B6451}|Account Hosting\\Account Hosting.csproj|c:\\users\\<USER>\\source\\repos\\account hosting\\account hosting\\services\\emailnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{4FF9D670-250B-444A-933B-2AD1306B6451}|Account Hosting\\Account Hosting.csproj|solutionrelative:account hosting\\services\\emailnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{e506b91c-c606-466a-90a9-123d1d1e12b3}"}, {"$type": "Bookmark", "Name": "ST:0:0:{eefa5220-e298-11d0-8f78-00a0c9110057}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ConfigurationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\Account Hosting\\Services\\ConfigurationService.cs", "RelativeDocumentMoniker": "Account Hosting\\Services\\ConfigurationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\Account Hosting\\Services\\ConfigurationService.cs", "RelativeToolTip": "Account Hosting\\Services\\ConfigurationService.cs", "ViewState": "AgIAAAcAAAAAAAAAAADwv1UAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-13T17:00:25.284Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "NotificationSchedulerService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\Account Hosting\\Services\\NotificationSchedulerService.cs", "RelativeDocumentMoniker": "Account Hosting\\Services\\NotificationSchedulerService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\Account Hosting\\Services\\NotificationSchedulerService.cs", "RelativeToolTip": "Account Hosting\\Services\\NotificationSchedulerService.cs", "ViewState": "AgIAAFoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-13T17:00:14.587Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "BaseViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\Account Hosting\\ViewModels\\BaseViewModel.cs", "RelativeDocumentMoniker": "Account Hosting\\ViewModels\\BaseViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\Account Hosting\\ViewModels\\BaseViewModel.cs", "RelativeToolTip": "Account Hosting\\ViewModels\\BaseViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-13T17:00:03.577Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "MainViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\Account Hosting\\ViewModels\\MainViewModel.cs", "RelativeDocumentMoniker": "Account Hosting\\ViewModels\\MainViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\Account Hosting\\ViewModels\\MainViewModel.cs", "RelativeToolTip": "Account Hosting\\ViewModels\\MainViewModel.cs", "ViewState": "AgIAAAIBAAAAAAAAAAAAAPgAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-13T16:59:07.996Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "EmailNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\Account Hosting\\Services\\EmailNotificationService.cs", "RelativeDocumentMoniker": "Account Hosting\\Services\\EmailNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\Account Hosting\\Services\\EmailNotificationService.cs", "RelativeToolTip": "Account Hosting\\Services\\EmailNotificationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-13T16:58:26.353Z", "EditorCaption": ""}]}]}]}