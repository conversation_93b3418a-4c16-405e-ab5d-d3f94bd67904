; سكريبت Inno Setup لإنشاء مثبت Account Hosting Manager
; Inno Setup Script for Account Hosting Manager

#define MyAppName "Account Hosting Manager"
#define MyAppVersion "1.2.0"
#define MyAppPublisher "Account Hosting Team"
#define MyAppURL "https://github.com/your-repo/account-hosting"
#define MyAppExeName "Account Hosting.exe"
#define MyAppAssocName MyAppName + " File"
#define MyAppAssocExt ".json"
#define MyAppAssocKey StringChange(MyAppAssocName, " ", "") + MyAppAssocExt

[Setup]
; معلومات التطبيق
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\AccountHosting
ChangesAssociations=yes
DisableProgramGroupPage=yes
LicenseFile=LICENSE
InfoBeforeFile=README.md
OutputDir=Installer
OutputBaseFilename=AccountHostingManager_Setup_v{#MyAppVersion}
SetupIconFile=icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

; متطلبات النظام
MinVersion=10.0
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; اللغة العربية
ShowLanguageDialog=yes

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
; ملفات التطبيق الرئيسية
Source: "bin\Release\net9.0-windows\win-x64\publish\{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion
Source: "bin\Release\net9.0-windows\win-x64\publish\*.dll"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "bin\Release\net9.0-windows\win-x64\publish\*.pdb"; DestDir: "{app}"; Flags: ignoreversion

; ملفات التوثيق
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "USER_GUIDE.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "DEVELOPER_GUIDE.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "CHANGELOG.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "DATA_LOCATION_GUIDE.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "LICENSE"; DestDir: "{app}"; Flags: ignoreversion

; ملفات الإعدادات والبيانات التجريبية
Source: "appsettings.json"; DestDir: "{app}"; Flags: ignoreversion
Source: "sample-data.json"; DestDir: "{app}"; Flags: ignoreversion

[Registry]
; ربط ملفات JSON بالتطبيق (اختياري)
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocExt}\OpenWithProgids"; ValueType: string; ValueName: "{#MyAppAssocKey}"; ValueData: ""; Flags: uninsdeletevalue
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}"; ValueType: string; ValueName: ""; ValueData: "{#MyAppAssocName}"; Flags: uninsdeletekey
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\{#MyAppExeName},0"
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\{#MyAppExeName}"" ""%1"""
Root: HKA; Subkey: "Software\Classes\Applications\{#MyAppExeName}\SupportedTypes"; ValueType: string; ValueName: ".json"; ValueData: ""

[Icons]
; اختصارات قائمة ابدأ
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autoprograms}\دليل المستخدم"; Filename: "{app}\USER_GUIDE.md"
Name: "{autoprograms}\إلغاء تثبيت {#MyAppName}"; Filename: "{uninstallexe}"

; اختصار سطح المكتب
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

; اختصار شريط المهام السريع
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: quicklaunchicon

[Run]
; تشغيل التطبيق بعد التثبيت
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

; فتح دليل المستخدم
Filename: "{app}\USER_GUIDE.md"; Description: "فتح دليل المستخدم"; Flags: nowait postinstall skipifsilent shellexec unchecked

[UninstallDelete]
; حذف ملفات إضافية عند إلغاء التثبيت
Type: filesandordirs; Name: "{app}\logs"
Type: filesandordirs; Name: "{app}\temp"

[Code]
// كود Pascal لتخصيص المثبت

// التحقق من متطلبات النظام
function InitializeSetup(): Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  
  // التحقق من إصدار Windows
  if Version.Major < 10 then
  begin
    MsgBox('هذا التطبيق يتطلب Windows 10 أو أحدث.', mbError, MB_OK);
    Result := False;
  end
  else
    Result := True;
end;

// رسالة ترحيب مخصصة
function NextButtonClick(CurPageID: Integer): Boolean;
begin
  if CurPageID = wpWelcome then
  begin
    MsgBox('مرحباً بك في مثبت Account Hosting Manager!' + #13#10 + 
           'هذا التطبيق سيساعدك في إدارة حسابات الاستضافة بسهولة.', 
           mbInformation, MB_OK);
  end;
  Result := True;
end;

// إنشاء مجلد البيانات
procedure CurStepChanged(CurStep: TSetupStep);
var
  DataDir: String;
begin
  if CurStep = ssPostInstall then
  begin
    // إنشاء مجلد البيانات في AppData
    DataDir := ExpandConstant('{userappdata}\AccountHosting');
    if not DirExists(DataDir) then
      CreateDir(DataDir);
      
    // نسخ البيانات التجريبية إذا لم تكن موجودة
    if not FileExists(DataDir + '\accounts.json') then
      FileCopy(ExpandConstant('{app}\sample-data.json'), 
               DataDir + '\accounts.json', False);
  end;
end;

// رسالة إكمال التثبيت
procedure CurPageChanged(CurPageID: Integer);
begin
  if CurPageID = wpFinished then
  begin
    WizardForm.FinishedLabel.Caption := 
      'تم تثبيت Account Hosting Manager بنجاح!' + #13#10#13#10 +
      'مكان التثبيت: ' + ExpandConstant('{app}') + #13#10 +
      'مكان البيانات: ' + ExpandConstant('{userappdata}\AccountHosting') + #13#10#13#10 +
      'يمكنك الآن بدء استخدام التطبيق لإدارة حسابات الاستضافة.';
  end;
end;
