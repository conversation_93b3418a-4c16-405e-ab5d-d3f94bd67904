﻿using System.Windows;
using Account_Hosting.Services;

namespace Account_Hosting;

/// <summary>
/// تطبيق إدارة الحسابات
/// </summary>
public partial class App : Application
{
    private NotificationSchedulerService? _notificationScheduler;

    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // تهيئة خدمة الإشعارات التلقائية
        var dataService = new DataService();
        var whatsAppService = new WhatsAppNotificationService();
        _notificationScheduler = new NotificationSchedulerService(dataService, whatsAppService);

        // بدء خدمة المراقبة التلقائية
        _notificationScheduler.Start();
    }

    protected override void OnExit(ExitEventArgs e)
    {
        // إيقاف خدمة المراقبة عند إغلاق التطبيق
        _notificationScheduler?.Stop();
        _notificationScheduler?.Dispose();

        base.OnExit(e);
    }
}

