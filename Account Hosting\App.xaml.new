<Application x:Class="Account_Hosting.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:Account_Hosting"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <!-- الألوان الرئيسية لتصميم Flat UI -->
        <Color x:Key="PrimaryColor">#2196F3</Color>
        <Color x:Key="PrimaryDarkColor">#1976D2</Color>
        <Color x:Key="PrimaryLightColor">#BBDEFB</Color>
        <Color x:Key="AccentColor">#FF4081</Color>
        <Color x:Key="Background">#F5F5F5</Color>
        <Color x:Key="SurfaceColor">#FFFFFF</Color>
        <Color x:Key="TextColor">#212121</Color>
        <Color x:Key="SecondaryTextColor">#757575</Color>
        <Color x:Key="DividerColor">#BDBDBD</Color>

        <!-- تحويل الألوان إلى فرش -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
        <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
        <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
        <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource Background}"/>
        <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
        <SolidColorBrush x:Key="TextBrush" Color="{StaticResource TextColor}"/>
        <SolidColorBrush x:Key="SecondaryTextBrush" Color="{StaticResource SecondaryTextColor}"/>
        <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource DividerColor}"/>

        <!-- أنماط الأزرار -->
        <Style x:Key="FlatButton" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                BorderThickness="0">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource FlatButton}">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>

        <!-- نموذج النافذة الرئيسي -->
        <Style x:Key="CustomWindow" TargetType="Window">
            <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
            <Setter Property="ResizeMode" Value="CanResizeWithGrip"/>
            <Setter Property="WindowStyle" Value="None"/>
            <Setter Property="AllowsTransparency" Value="True"/>
            <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Window">
                        <Grid>
                            <!-- شريط العنوان المخصص -->
                            <Grid Height="40" Background="{StaticResource PrimaryBrush}" MouseLeftButtonDown="TitleBar_MouseDown">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- عنوان البرنامج -->
                                <TextBlock Grid.Column="0" Text="إدارة الحسابات" VerticalAlignment="Center" Margin="10,0" Foreground="White" FontWeight="Light" FontSize="16"/>

                                <!-- أزرار التحكم -->
                                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                                    <Button x:Name="MinimizeButton" Style="{StaticResource WindowButton}" Click="MinimizeButton_Click" ToolTip="تصغير">
                                        <Path Data="M0,0 L8,0" Stroke="White" StrokeThickness="1" VerticalAlignment="Center" HorizontalAlignment="Center" Margin="0,2"/>
                                    </Button>
                                    <Button x:Name="CloseButton" Style="{StaticResource WindowButton}" Click="CloseButton_Click" ToolTip="إغلاق">
                                        <Path Data="M0,0 L6,6 M6,0 L0,6" Stroke="White" StrokeThickness="1" VerticalAlignment="Center" HorizontalAlignment="Center" Margin="0,2"/>
                                    </Button>
                                </StackPanel>
                            </Grid>

                            <!-- محتوى النافذة -->
                            <ContentPresenter Grid.Row="1" Margin="0,40,0,0"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط الأزرار في شريط العنوان -->
        <Style x:Key="WindowButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Margin" Value="0,0,2,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="2">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#2B2B2B"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Application.Resources>
</Application>