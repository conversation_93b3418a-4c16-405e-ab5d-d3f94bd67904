@echo off
title بناء جميع أنواع المثبتات - Account Hosting Manager
chcp 65001 >nul
echo.
echo ================================================
echo    بناء جميع أنواع المثبتات
echo    Account Hosting Manager v1.2.0
echo ================================================
echo.

REM التحقق من وجود dotnet
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: .NET SDK غير مثبت
    echo يرجى تثبيت .NET 9 SDK من: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo 🔨 الخطوة 1: بناء التطبيق...
dotnet build -c Release
if errorlevel 1 (
    echo ❌ خطأ في بناء التطبيق
    pause
    exit /b 1
)

echo 📦 الخطوة 2: نشر التطبيق (Self-Contained)...
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
if errorlevel 1 (
    echo ❌ خطأ في نشر التطبيق
    pause
    exit /b 1
)

echo 🗂️ الخطوة 3: إنشاء مجلد المثبتات...
if exist "Installers" rmdir /s /q "Installers"
mkdir "Installers"

echo 📋 الخطوة 4: إنشاء مثبت بسيط (Portable)...
mkdir "Installers\Portable"
xcopy /E /I /Y "bin\Release\net9.0-windows\win-x64\publish\*" "Installers\Portable\"

REM إنشاء ملف تشغيل للنسخة المحمولة
echo @echo off > "Installers\Portable\Run.bat"
echo title Account Hosting Manager >> "Installers\Portable\Run.bat"
echo start "" "Account Hosting.exe" >> "Installers\Portable\Run.bat"

REM إنشاء ملف README للنسخة المحمولة
echo # Account Hosting Manager - النسخة المحمولة > "Installers\Portable\README.txt"
echo. >> "Installers\Portable\README.txt"
echo هذه نسخة محمولة من التطبيق لا تحتاج تثبيت >> "Installers\Portable\README.txt"
echo. >> "Installers\Portable\README.txt"
echo للتشغيل: >> "Installers\Portable\README.txt"
echo 1. شغل ملف "Account Hosting.exe" >> "Installers\Portable\README.txt"
echo 2. أو شغل ملف "Run.bat" >> "Installers\Portable\README.txt"
echo. >> "Installers\Portable\README.txt"
echo البيانات ستُحفظ في: %%AppData%%\AccountHosting >> "Installers\Portable\README.txt"

echo 🛠️ الخطوة 5: تشغيل PowerShell لإنشاء المثبت المتقدم...
powershell -ExecutionPolicy Bypass -File "CreateInstaller.ps1"

echo 📦 الخطوة 6: إنشاء ملفات ZIP للتوزيع...
powershell -Command "Compress-Archive -Path 'Installers\Portable' -DestinationPath 'Installers\AccountHostingManager_Portable_v1.2.0.zip' -Force"

echo 📄 الخطوة 7: إنشاء ملف معلومات الإصدار...
echo Account Hosting Manager v1.2.0 > "Installers\RELEASE_INFO.txt"
echo تاريخ البناء: %date% %time% >> "Installers\RELEASE_INFO.txt"
echo. >> "Installers\RELEASE_INFO.txt"
echo الملفات المتوفرة: >> "Installers\RELEASE_INFO.txt"
echo - AccountHostingManager_Portable_v1.2.0.zip (نسخة محمولة) >> "Installers\RELEASE_INFO.txt"
echo - AccountHostingManager_v1.2.0.zip (مثبت بسيط) >> "Installers\RELEASE_INFO.txt"
echo. >> "Installers\RELEASE_INFO.txt"
echo متطلبات النظام: >> "Installers\RELEASE_INFO.txt"
echo - Windows 10/11 (x64) >> "Installers\RELEASE_INFO.txt"
echo - .NET 9.0 Runtime (مضمن) >> "Installers\RELEASE_INFO.txt"
echo. >> "Installers\RELEASE_INFO.txt"
echo التثبيت: >> "Installers\RELEASE_INFO.txt"
echo 1. حمل الملف المناسب >> "Installers\RELEASE_INFO.txt"
echo 2. فك الضغط >> "Installers\RELEASE_INFO.txt"
echo 3. شغل Install.bat كمسؤول (للمثبت) >> "Installers\RELEASE_INFO.txt"
echo    أو شغل Account Hosting.exe مباشرة (للنسخة المحمولة) >> "Installers\RELEASE_INFO.txt"

echo.
echo ✅ تم إنشاء جميع المثبتات بنجاح!
echo.
echo 📁 الملفات المتوفرة في مجلد Installers:
echo    📦 AccountHostingManager_Portable_v1.2.0.zip
echo    📦 AccountHostingManager_v1.2.0.zip
echo    📂 Portable\ (مجلد النسخة المحمولة)
echo    📂 AccountHostingInstaller\ (مجلد المثبت)
echo.
echo 🎉 المثبتات جاهزة للتوزيع!
echo.

REM فتح مجلد المثبتات
set /p open=هل تريد فتح مجلد المثبتات؟ (Y/N): 
if /i "%open%"=="Y" start explorer "Installers"

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
