# 📝 سجل التغييرات - Account Hosting Manager

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.2.0] - 2024-12-08

### ✨ إضافات جديدة

- **زر تفعيل/تعطيل الحساب**: إمكانية تعطيل الحسابات يدوياً
- **نافذة حوار محسنة**: تصميم أكبر وأكثر وضوحاً (500x480)
- **حالة جديدة للحسابات**: عرض الحسابات المعطلة بحالة "⏸️ معطل"
- **أزرار محولة لليمين**: ترتيب الأزرار من اليمين لليسار في شريط الأدوات
- **شريط حالة محسن**: عرض إحصائيات مفصلة (نشط، معطل، ينتهي قريباً، منتهي)

### 🎨 تحسينات التصميم

- **نافذة الحوار**: حجم أكبر مع تخطيط محسن
- **CheckBox للنشاط**: واجهة سهلة لتفعيل/تعطيل الحسابات
- **ترتيب الأزرار**: من اليمين لليسار (إضافة، تعديل، حذف، تحديث)
- **ألوان الحالة**: لون رمادي للحسابات المعطلة
- **نصوص توضيحية**: إرشادات واضحة للمستخدم

### 🔧 تحسينات وظيفية

- **تجاهل الحسابات المعطلة**: لا يتم إرسال إشعارات للحسابات المعطلة
- **إحصائيات شاملة**: عرض تفصيلي لحالات جميع الحسابات
- **بيانات تجريبية محدثة**: أمثلة تشمل حسابات بحالات مختلفة
- **منطق محسن**: تحديث الخصائص المحسوبة لتراعي حالة النشاط

---

## [1.1.0] - 2024-12-08

### ✨ إضافات جديدة

- **دعم RTL كامل**: واجهة من اليمين إلى اليسار لتحسين تجربة المستخدم العربي
- **تاريخ إنشاء قابل للتعديل**: إمكانية إدخال تاريخ الإنشاء يدوياً
- **أزرار محسنة**: تصميم أفضل مع أيقونات واضحة ونصوص وصفية
- **زر إضافة سريع**: في شريط العنوان للوصول السريع
- **تحسينات بصرية**: ألوان وأحجام أفضل للأزرار

### 🎨 تحسينات التصميم

- **FlowDirection**: دعم RTL في جميع العناصر
- **أزرار أكبر**: حجم خط 15px وحشو محسن
- **أيقونات محسنة**: رموز تعبيرية أوضح ونصوص وصفية
- **DatePicker**: لاختيار تاريخ الإنشاء مع دعم التقويم العربي
- **شريط حالة محسن**: خط عريض وتوجيه RTL

### 🔧 تحسينات وظيفية

- **تاريخ الإنشاء المرن**: يمكن تعديله في نافذة الحوار
- **حساب تلقائي للاستحقاق**: بناءً على تاريخ الإنشاء المدخل
- **تحسين التنقل**: أزرار أوضح مع tooltips مفصلة
- **تجربة مستخدم أفضل**: واجهة أكثر سهولة وبديهية

---

## [1.0.0] - 2024-12-08

### ✨ إضافات جديدة

- **واجهة مستخدم عصرية**: تصميم Flat UI بألوان هادئة وأيقونات حديثة
- **إدارة شاملة للحسابات**: إضافة، تعديل، وحذف الحسابات
- **نظام إشعارات واتساب**: إرسال تنبيهات تلقائية قبل 30 يوم من الانتهاء
- **تخزين JSON محلي**: حفظ البيانات بدون الحاجة لقاعدة بيانات
- **مراقبة تلقائية**: فحص الحسابات كل ساعة تلقائياً
- **حالات بصرية**: عرض حالة كل حساب (نشط، قريب الانتهاء، منتهي)
- **شريط حالة معلوماتي**: عرض إحصائيات سريعة
- **نمط MVVM**: هيكلة نظيفة ومنظمة للكود

### 🏗️ الهيكل التقني

- **Models**: نموذج Account مع خصائص محسوبة للحالة
- **ViewModels**: MainViewModel مع BaseViewModel للـ INotifyPropertyChanged
- **Services**:
  - DataService لإدارة ملفات JSON
  - WhatsAppNotificationService لإرسال الإشعارات
  - NotificationSchedulerService للمراقبة التلقائية
  - ConfigurationService لإدارة الإعدادات
- **Views**: نافذة رئيسية ونافذة حوار للحسابات
- **Commands**: RelayCommand لتنفيذ الأوامر
- **Converters**: محولات للواجهة

### 🎨 التصميم

- **ألوان أساسية**: أزرق (#007ACC)، أخضر (#28A745)، أصفر (#FFC107)، أحمر (#DC3545)
- **خلفية**: رمادي فاتح (#F8F9FA)
- **أزرار**: تصميم مسطح مع تأثيرات hover
- **جدول البيانات**: تصميم نظيف مع ألوان متناوبة
- **أيقونات**: رموز تعبيرية حديثة

### 📋 الحقول المدعومة

- **معرف الحساب**: رقم فريد تلقائي
- **اسم الحساب**: نص قابل للتخصيص
- **السعر**: رقم عشري بتنسيق عملة
- **تاريخ الإنشاء**: يُحدد تلقائياً عند الإضافة
- **تاريخ الاستحقاق**: يُحسب تلقائياً (سنة من تاريخ الإنشاء)
- **حالة الإشعار**: تتبع إرسال إشعارات واتساب

### 🔔 نظام الإشعارات

- **فحص تلقائي**: كل ساعة
- **شرط الإرسال**: قبل 30 يوم من الانتهاء
- **منع التكرار**: لا يُرسل إشعار مكرر
- **دعم cURL**: أمثلة لـ Twilio وWhatsApp Business API
- **رسائل مخصصة**: تتضمن اسم الحساب والسعر والتاريخ

### 📁 الملفات المضافة

```
Account Hosting/
├── Models/Account.cs
├── ViewModels/BaseViewModel.cs
├── ViewModels/MainViewModel.cs
├── Views/AccountDialog.xaml
├── Views/AccountDialog.xaml.cs
├── Services/DataService.cs
├── Services/WhatsAppNotificationService.cs
├── Services/NotificationSchedulerService.cs
├── Services/ConfigurationService.cs
├── Commands/RelayCommand.cs
├── Converters/BooleanToVisibilityConverter.cs
├── appsettings.json
├── sample-data.json
├── README.md
├── USER_GUIDE.md
├── DEVELOPER_GUIDE.md
└── CHANGELOG.md
```

### 🔧 الإعدادات

- **ملف التكوين**: appsettings.json
- **إعدادات واتساب**: URL، رقم الهاتف، رمز API
- **إعدادات التطبيق**: اسم ملف البيانات، اللغة
- **إعدادات الواجهة**: الألوان والمظهر

### 📖 التوثيق

- **دليل المستخدم**: تعليمات شاملة للاستخدام
- **دليل المطور**: إرشادات للتطوير والتخصيص
- **أمثلة cURL**: لمختلف خدمات واتساب
- **بيانات تجريبية**: ملف JSON للاختبار

### 🛠️ المتطلبات التقنية

- **.NET 9.0**: أحدث إصدار من .NET
- **WPF**: لواجهة المستخدم
- **Newtonsoft.Json**: لمعالجة JSON
- **Windows 10/11**: نظام التشغيل المدعوم

### 🔒 الأمان والموثوقية

- **تشفير البيانات**: إمكانية إضافة تشفير للبيانات الحساسة
- **نسخ احتياطية**: حفظ تلقائي للبيانات
- **معالجة الأخطاء**: try-catch شامل
- **تسجيل الأحداث**: Debug.WriteLine للمراقبة

---

## 🚀 الخطط المستقبلية

### [1.1.0] - مخطط له

- [ ] إضافة تقارير مفصلة
- [ ] تصدير البيانات إلى Excel
- [ ] إشعارات بريد إلكتروني
- [ ] نافذة إعدادات متقدمة
- [ ] دعم قواعد البيانات SQL

### [1.2.0] - مخطط له

- [ ] واجهة متعددة اللغات
- [ ] مزامنة سحابية
- [ ] تطبيق جوال مصاحب
- [ ] تقارير رسوم بيانية
- [ ] نظام مستخدمين متعدد

---

**للمزيد من المعلومات، راجع [README.md](README.md) و [USER_GUIDE.md](USER_GUIDE.md)**
