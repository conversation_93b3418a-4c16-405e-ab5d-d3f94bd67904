using System;
using System.Windows.Input;

namespace Account_Hosting.Commands
{
    /// <summary>
    /// تنفيذ بسيط لـ ICommand يسمح بتمرير دوال للتنفيذ والتحقق من الإمكانية
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action<object?> _execute;
        private readonly Func<object?, bool>? _canExecute;

        /// <summary>
        /// ينشئ أمر جديد
        /// </summary>
        /// <param name="execute">الدالة التي سيتم تنفيذها</param>
        /// <param name="canExecute">الدالة التي تحدد إمكانية التنفيذ (اختيارية)</param>
        public RelayCommand(Action<object?> execute, Func<object?, bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        /// <summary>
        /// ينشئ أمر جديد بدون معاملات
        /// </summary>
        /// <param name="execute">الدالة التي سيتم تنفيذها</param>
        /// <param name="canExecute">الدالة التي تحدد إمكانية التنفيذ (اختيارية)</param>
        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = _ => execute();
            _canExecute = canExecute != null ? _ => canExecute() : null;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter)
        {
            return _canExecute?.Invoke(parameter) ?? true;
        }

        public void Execute(object? parameter)
        {
            _execute(parameter);
        }

        /// <summary>
        /// يثير حدث CanExecuteChanged لإعادة تقييم إمكانية تنفيذ الأمر
        /// </summary>
        public void RaiseCanExecuteChanged()
        {
            CommandManager.InvalidateRequerySuggested();
        }
    }
}
