# سكريبت إنشاء مثبت Account Hosting Manager
# PowerShell Script to Create Installer

param(
    [string]$OutputPath = ".\Installer",
    [string]$AppName = "Account Hosting Manager",
    [string]$Version = "1.2.0"
)

Write-Host "🚀 بدء إنشاء مثبت $AppName..." -ForegroundColor Green

# إنشاء مجلد المثبت
$InstallerDir = Join-Path $OutputPath "AccountHostingInstaller"
if (Test-Path $InstallerDir) {
    Remove-Item $InstallerDir -Recurse -Force
}
New-Item -ItemType Directory -Path $InstallerDir -Force | Out-Null

# نسخ الملفات المنشورة
$PublishPath = ".\bin\Release\net9.0-windows\win-x64\publish"
if (-not (Test-Path $PublishPath)) {
    Write-Host "❌ خطأ: لم يتم العثور على ملفات النشر. يرجى تشغيل dotnet publish أولاً" -ForegroundColor Red
    exit 1
}

Write-Host "📁 نسخ ملفات التطبيق..." -ForegroundColor Yellow
Copy-Item "$PublishPath\*" -Destination $InstallerDir -Recurse -Force

# إنشاء ملف التثبيت
$InstallScript = @"
@echo off
title تثبيت $AppName
echo.
echo ================================================
echo           تثبيت $AppName v$Version
echo ================================================
echo.

REM تحديد مجلد التثبيت
set "INSTALL_DIR=%ProgramFiles%\AccountHosting"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Account Hosting Manager.lnk"
set "START_MENU_SHORTCUT=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Account Hosting Manager.lnk"

echo 📁 إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 📋 نسخ ملفات التطبيق...
xcopy /E /I /Y "%~dp0*" "%INSTALL_DIR%\" >nul

echo 🔗 إنشاء اختصارات...
REM إنشاء اختصار سطح المكتب
powershell -Command "& {`$WshShell = New-Object -comObject WScript.Shell; `$Shortcut = `$WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); `$Shortcut.TargetPath = '%INSTALL_DIR%\Account Hosting.exe'; `$Shortcut.WorkingDirectory = '%INSTALL_DIR%'; `$Shortcut.Description = 'Account Hosting Manager - إدارة الحسابات'; `$Shortcut.Save()}"

REM إنشاء اختصار قائمة ابدأ
powershell -Command "& {`$WshShell = New-Object -comObject WScript.Shell; `$Shortcut = `$WshShell.CreateShortcut('%START_MENU_SHORTCUT%'); `$Shortcut.TargetPath = '%INSTALL_DIR%\Account Hosting.exe'; `$Shortcut.WorkingDirectory = '%INSTALL_DIR%'; `$Shortcut.Description = 'Account Hosting Manager - إدارة الحسابات'; `$Shortcut.Save()}"

echo.
echo ✅ تم التثبيت بنجاح!
echo.
echo 📍 مكان التثبيت: %INSTALL_DIR%
echo 🖥️  اختصار سطح المكتب: تم إنشاؤه
echo 📋 قائمة ابدأ: تم إنشاؤه
echo.
echo اضغط أي مفتاح لإغلاق النافذة...
pause >nul
"@

$InstallScript | Out-File -FilePath (Join-Path $InstallerDir "Install.bat") -Encoding UTF8

# إنشاء ملف إلغاء التثبيت
$UninstallScript = @"
@echo off
title إلغاء تثبيت $AppName
echo.
echo ================================================
echo         إلغاء تثبيت $AppName v$Version
echo ================================================
echo.

set "INSTALL_DIR=%ProgramFiles%\AccountHosting"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Account Hosting Manager.lnk"
set "START_MENU_SHORTCUT=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Account Hosting Manager.lnk"

echo ⚠️  هذا سيقوم بحذف التطبيق نهائياً
echo    (البيانات في %%AppData%% ستبقى محفوظة)
echo.
set /p confirm=هل أنت متأكد؟ (Y/N): 
if /i not "%confirm%"=="Y" goto :cancel

echo 🗑️  حذف ملفات التطبيق...
if exist "%INSTALL_DIR%" rmdir /s /q "%INSTALL_DIR%"

echo 🔗 حذف الاختصارات...
if exist "%DESKTOP_SHORTCUT%" del "%DESKTOP_SHORTCUT%"
if exist "%START_MENU_SHORTCUT%" del "%START_MENU_SHORTCUT%"

echo.
echo ✅ تم إلغاء التثبيت بنجاح!
echo    البيانات محفوظة في: %%AppData%%\AccountHosting
echo.
goto :end

:cancel
echo ❌ تم إلغاء العملية
echo.

:end
echo اضغط أي مفتاح لإغلاق النافذة...
pause >nul
"@

$UninstallScript | Out-File -FilePath (Join-Path $InstallerDir "Uninstall.bat") -Encoding UTF8

# إنشاء ملف README
$ReadmeContent = @"
# 📦 مثبت Account Hosting Manager

## 🚀 التثبيت:
1. شغل ملف `Install.bat` كمسؤول
2. اتبع التعليمات على الشاشة
3. سيتم إنشاء اختصارات على سطح المكتب وقائمة ابدأ

## 🗑️ إلغاء التثبيت:
1. شغل ملف `Uninstall.bat` كمسؤول
2. أكد العملية
3. البيانات ستبقى محفوظة في %AppData%\AccountHosting

## 📁 مكان التثبيت:
`C:\Program Files\AccountHosting\`

## 💾 مكان البيانات:
`%AppData%\AccountHosting\accounts.json`

## ⚠️ متطلبات النظام:
- Windows 10/11
- .NET 9.0 Runtime (مضمن في المثبت)

## 📞 الدعم:
للمساعدة أو الإبلاغ عن مشاكل، راجع ملف USER_GUIDE.md
"@

$ReadmeContent | Out-File -FilePath (Join-Path $InstallerDir "README.txt") -Encoding UTF8

# إنشاء ملف معلومات الإصدار
$VersionInfo = @"
Account Hosting Manager v$Version
تاريخ البناء: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
النظام المستهدف: Windows x64
.NET Version: 9.0
"@

$VersionInfo | Out-File -FilePath (Join-Path $InstallerDir "VERSION.txt") -Encoding UTF8

Write-Host "✅ تم إنشاء المثبت بنجاح!" -ForegroundColor Green
Write-Host "📁 مكان المثبت: $InstallerDir" -ForegroundColor Cyan
Write-Host "🚀 لتثبيت التطبيق: شغل Install.bat كمسؤول" -ForegroundColor Yellow

# إنشاء ملف ZIP للتوزيع
$ZipPath = Join-Path $OutputPath "AccountHostingManager_v$Version.zip"
if (Test-Path $ZipPath) {
    Remove-Item $ZipPath -Force
}

Write-Host "📦 إنشاء ملف ZIP للتوزيع..." -ForegroundColor Yellow
Compress-Archive -Path $InstallerDir -DestinationPath $ZipPath -Force

Write-Host "✅ تم إنشاء ملف ZIP: $ZipPath" -ForegroundColor Green
Write-Host "🎉 المثبت جاهز للتوزيع!" -ForegroundColor Magenta
"@
