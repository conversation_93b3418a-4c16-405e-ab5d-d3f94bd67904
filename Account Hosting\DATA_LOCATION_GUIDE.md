# 📁 دليل مكان حفظ البيانات - Account Hosting Manager

## 🎯 **مكان حفظ البيانات:**

### 📍 **المسار الكامل:**

```
C:\Users\<USER>\AppData\Roaming\AccountHosting\accounts.json
```

### 🔍 **طرق الوصول للملف:**

#### **الطريقة الأولى - الأسرع:**

1. في التطبيق، اضغط على زر **"📁 مكان البيانات"** في الشريط العلوي
2. ستظهر نافذة تعرض المسار الكامل
3. اضغط **"نعم"** لفتح المجلد مباشرة

#### **الطريقة الثانية - يدوياً:**

1. اضغط `Windows + R`
2. اكتب: `%AppData%\AccountHosting`
3. اضغط Enter
4. ستجد ملف `accounts.json`

#### **الطريقة الثالثة - عبر File Explorer:**

1. افتح File Explorer
2. في شريط العنوان اكتب: `%AppData%\AccountHosting`
3. اضغط Enter

## 📄 **محتوى ملف البيانات:**

### 🔧 **تنسيق JSON:**

```json
[
  {
    "Id": 1,
    "AccountName": "استضافة موقع الشركة",
    "Price": 500.0,
    "CreatedDate": "2024-01-15T10:30:00",
    "DueDate": "2025-01-15T10:30:00",
    "EmailNotificationSent": false,
    "IsAccountActive": true
  }
]
```

### 📋 **شرح الحقول:**

- **Id**: معرف فريد للحساب
- **AccountName**: اسم الحساب
- **Price**: سعر الحساب
- **CreatedDate**: تاريخ إنشاء الحساب
- **DueDate**: تاريخ انتهاء الصلاحية
- **EmailNotificationSent**: هل تم إرسال إشعار بريد إلكتروني
- **IsAccountActive**: هل الحساب نشط أم معطل

## 🔄 **إدارة البيانات:**

### 💾 **النسخ الاحتياطي:**

1. انسخ ملف `accounts.json` إلى مكان آمن
2. يُنصح بعمل نسخة احتياطية أسبوعياً
3. احفظ النسخة في OneDrive أو Google Drive

### 📥 **استعادة البيانات:**

1. أغلق التطبيق
2. استبدل ملف `accounts.json` بالنسخة الاحتياطية
3. شغل التطبيق مرة أخرى

### 🔄 **نقل البيانات لجهاز آخر:**

1. انسخ ملف `accounts.json` من الجهاز القديم
2. في الجهاز الجديد، شغل التطبيق مرة واحدة لإنشاء المجلد
3. استبدل الملف الجديد بالملف المنسوخ

## ⚠️ **تحذيرات مهمة:**

### 🚫 **لا تفعل:**

- لا تحذف مجلد `AccountHosting` كاملاً
- لا تغير اسم ملف `accounts.json`
- لا تعدل الملف والتطبيق مفتوح

### ✅ **افعل:**

- أغلق التطبيق قبل تعديل الملف
- اعمل نسخة احتياطية قبل أي تعديل
- تأكد من صحة تنسيق JSON

## 🛠️ **حل المشاكل الشائعة:**

### ❌ **المشكلة: لا أجد المجلد**

**الحل:**

1. تأكد من إظهار الملفات المخفية في Windows
2. أو استخدم زر "📁 مكان البيانات" في التطبيق

### ❌ **المشكلة: الملف تالف**

**الحل:**

1. أغلق التطبيق
2. احذف ملف `accounts.json`
3. شغل التطبيق (سينشئ ملف جديد فارغ)
4. استعد من النسخة الاحتياطية

### ❌ **المشكلة: البيانات لا تُحفظ**

**الحل:**

1. تأكد من صلاحيات الكتابة في المجلد
2. شغل التطبيق كمسؤول
3. تحقق من مساحة القرص الصلب

## 📱 **نصائح للاستخدام الأمثل:**

### 🔐 **الأمان:**

- لا تشارك ملف البيانات مع أشخاص غير مخولين
- اعمل كلمة مرور للجهاز
- استخدم برامج مكافحة الفيروسات

### 📊 **التنظيم:**

- اعمل نسخة احتياطية شهرياً
- احتفظ بنسخ متعددة (أسبوعية، شهرية)
- سمي النسخ بالتاريخ (مثل: accounts_2024_12_08.json)

### 🚀 **الأداء:**

- لا تحتفظ بآلاف الحسابات في ملف واحد
- احذف الحسابات القديمة غير المطلوبة
- استخدم أسماء واضحة للحسابات

---

**💡 نصيحة:** استخدم زر "📁 مكان البيانات" في التطبيق للوصول السريع للملف!
