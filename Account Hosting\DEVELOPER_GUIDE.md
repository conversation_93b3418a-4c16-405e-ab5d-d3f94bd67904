# 👨‍💻 دليل المطور - Account Hosting Manager

## 🏗️ هيكل المشروع

### نمط MVVM

المشروع يتبع نمط Model-View-ViewModel لفصل منطق العمل عن واجهة المستخدم:

- **Models**: نماذج البيانات (`Account.cs`)
- **Views**: واجهات المستخدم (XAML files)
- **ViewModels**: منطق العرض والربط (`MainViewModel.cs`, `BaseViewModel.cs`)

### الخدمات (Services)

- `DataService`: إدارة تخزين واسترجاع البيانات من JSON
- `EmailNotificationService`: إرسال إشعارات البريد الإلكتروني
- `NotificationSchedulerService`: المراقبة التلقائية للإشعارات
- `ConfigurationService`: إدارة إعدادات التطبيق

## 🔧 إضافة ميزات جديدة

### 1. إضافة حقل جديد للحساب

#### في `Models/Account.cs`:

```csharp
/// <summary>
/// وصف الحقل الجديد
/// </summary>
public string NewField { get; set; } = string.Empty;
```

#### في `Views/AccountDialog.xaml`:

```xml
<Label Content="الحقل الجديد:" Style="{StaticResource LabelStyle}"/>
<TextBox Text="{Binding NewField, UpdateSourceTrigger=PropertyChanged}"
         Style="{StaticResource FlatTextBoxStyle}"/>
```

#### في `MainWindow.xaml` (DataGrid):

```xml
<DataGridTextColumn Header="الحقل الجديد"
                    Binding="{Binding NewField}"
                    Width="120"
                    IsReadOnly="True"/>
```

### 2. إضافة أمر جديد

#### في `ViewModels/MainViewModel.cs`:

```csharp
public ICommand NewCommand { get; }

// في الكونستركتور
NewCommand = new RelayCommand(ExecuteNewCommand, CanExecuteNewCommand);

private void ExecuteNewCommand()
{
    // منطق تنفيذ الأمر
}

private bool CanExecuteNewCommand()
{
    // شروط تنفيذ الأمر
    return true;
}
```

#### في `MainWindow.xaml`:

```xml
<Button Content="أمر جديد"
        Style="{StaticResource FlatButtonStyle}"
        Command="{Binding NewCommand}"/>
```

### 3. إضافة خدمة جديدة

```csharp
namespace Account_Hosting.Services
{
    public class NewService
    {
        public async Task<bool> DoSomethingAsync()
        {
            // منطق الخدمة
            return true;
        }
    }
}
```

## 🎨 تخصيص التصميم

### إضافة نمط جديد

في `MainWindow.xaml` ضمن `Window.Resources`:

```xml
<Style x:Key="NewButtonStyle" TargetType="Button" BasedOn="{StaticResource FlatButtonStyle}">
    <Setter Property="Background" Value="#6F42C1"/>
    <Style.Triggers>
        <Trigger Property="IsMouseOver" Value="True">
            <Setter Property="Background" Value="#5A32A3"/>
        </Trigger>
    </Style.Triggers>
</Style>
```

### تغيير الألوان الأساسية

في `appsettings.json`:

```json
{
  "UISettings": {
    "PrimaryColor": "#YOUR_COLOR",
    "SecondaryColor": "#YOUR_COLOR"
  }
}
```

## 📊 إضافة تقارير

### 1. إنشاء نموذج التقرير

```csharp
public class AccountReport
{
    public int TotalAccounts { get; set; }
    public int ActiveAccounts { get; set; }
    public int ExpiringSoonAccounts { get; set; }
    public int ExpiredAccounts { get; set; }
    public decimal TotalRevenue { get; set; }
}
```

### 2. إضافة خدمة التقارير

```csharp
public class ReportService
{
    public AccountReport GenerateReport(List<Account> accounts)
    {
        return new AccountReport
        {
            TotalAccounts = accounts.Count,
            ActiveAccounts = accounts.Count(a => a.IsActive),
            ExpiringSoonAccounts = accounts.Count(a => a.IsExpiringSoon),
            ExpiredAccounts = accounts.Count(a => a.IsExpired),
            TotalRevenue = accounts.Sum(a => a.Price)
        };
    }
}
```

## 🔔 تخصيص الإشعارات

### إضافة قنوات إشعار جديدة

#### إشعارات البريد الإلكتروني:

```csharp
public class EmailNotificationService
{
    public async Task<bool> SendEmailNotificationAsync(Account account, string emailAddress)
    {
        // منطق إرسال البريد الإلكتروني
        return true;
    }
}
```

#### إشعارات SMS:

```csharp
public class SmsNotificationService
{
    public async Task<bool> SendSmsNotificationAsync(Account account, string phoneNumber)
    {
        // منطق إرسال SMS
        return true;
    }
}
```

## 🗄️ تطوير قاعدة البيانات

### التحويل من JSON إلى SQL Server

#### 1. إضافة Entity Framework

```xml
<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
```

#### 2. إنشاء DbContext

```csharp
public class AccountDbContext : DbContext
{
    public DbSet<Account> Accounts { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder.UseSqlServer("Server=.;Database=AccountHosting;Trusted_Connection=true;");
    }
}
```

#### 3. تحديث DataService

```csharp
public class DatabaseDataService : IDataService
{
    private readonly AccountDbContext _context;

    public DatabaseDataService(AccountDbContext context)
    {
        _context = context;
    }

    public async Task<List<Account>> LoadAccountsAsync()
    {
        return await _context.Accounts.ToListAsync();
    }

    public async Task SaveAccountsAsync(List<Account> accounts)
    {
        _context.Accounts.UpdateRange(accounts);
        await _context.SaveChangesAsync();
    }
}
```

## 🧪 الاختبارات

### إضافة مشروع اختبار

```bash
dotnet new mstest -n Account.Hosting.Tests
dotnet add Account.Hosting.Tests reference Account.Hosting
```

### مثال اختبار وحدة

```csharp
[TestClass]
public class AccountTests
{
    [TestMethod]
    public void Account_IsExpiringSoon_ReturnsTrue_WhenDueDateIsWithin30Days()
    {
        // Arrange
        var account = new Account
        {
            DueDate = DateTime.Now.AddDays(15)
        };

        // Act
        var result = account.IsExpiringSoon;

        // Assert
        Assert.IsTrue(result);
    }
}
```

## 📱 إضافة واجهات جديدة

### نافذة الإعدادات

```xml
<Window x:Class="Account_Hosting.Views.SettingsWindow">
    <Grid>
        <TabControl>
            <TabItem Header="عام">
                <!-- إعدادات عامة -->
            </TabItem>
            <TabItem Header="واتساب">
                <!-- إعدادات واتساب -->
            </TabItem>
            <TabItem Header="المظهر">
                <!-- إعدادات المظهر -->
            </TabItem>
        </TabControl>
    </Grid>
</Window>
```

## 🔒 الأمان

### تشفير البيانات الحساسة

```csharp
public class EncryptionService
{
    public string Encrypt(string plainText, string key)
    {
        // منطق التشفير
        return encryptedText;
    }

    public string Decrypt(string encryptedText, string key)
    {
        // منطق فك التشفير
        return plainText;
    }
}
```

## 📝 أفضل الممارسات

1. **استخدم async/await** للعمليات غير المتزامنة
2. **أضف تعليقات XML** لجميع الطرق العامة
3. **استخدم using statements** لتحرير الموارد
4. **تحقق من null** قبل استخدام الكائنات
5. **استخدم try-catch** للتعامل مع الأخطاء
6. **اتبع نمط MVVM** بدقة
7. **اختبر الكود** قبل النشر

## 🚀 النشر

### إنشاء ملف تنفيذي

```bash
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
```

### إنشاء مثبت

استخدم أدوات مثل:

- WiX Toolset
- Inno Setup
- Advanced Installer

## 📞 الدعم التقني

لأي استفسارات تقنية:

1. راجع التوثيق أولاً
2. ابحث في Issues الموجودة
3. أنشئ Issue جديد مع تفاصيل المشكلة
4. تواصل مع فريق التطوير
