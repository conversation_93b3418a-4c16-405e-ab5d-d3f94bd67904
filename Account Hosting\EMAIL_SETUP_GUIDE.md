# 📧 دليل إعداد البريد الإلكتروني - Account Hosting Manager

## 🚀 **نظرة عامة**

تم تحديث التطبيق ليستخدم **البريد الإلكتروني** بدلاً من واتساب لإرسال الإشعارات. هذا الدليل يوضح كيفية إعداد البريد الإلكتروني للحصول على تنبيهات تلقائية.

## ⚙️ **إعداد البريد الإلكتروني**

### **الخطوة 1: تحديث ملف الإعدادات**

افتح ملف `appsettings.json` وحدث الإعدادات التالية:

```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "your-app-password",
    "RecipientEmail": "<EMAIL>",
    "SenderName": "نظام إدارة الحسابات",
    "EnableSsl": true,
    "NotificationDaysBeforeExpiry": 30,
    "CheckIntervalHours": 1
  }
}
```

### **الخطوة 2: إعداد Gmail (الأكثر شيوعاً)**

#### **أ) تفعيل المصادقة الثنائية:**
1. اذهب إلى [حساب Google](https://myaccount.google.com/)
2. اختر **الأمان** من القائمة الجانبية
3. فعّل **التحقق بخطوتين**

#### **ب) إنشاء كلمة مرور التطبيق:**
1. في صفحة الأمان، اختر **كلمات مرور التطبيقات**
2. اختر **التطبيق**: بريد
3. اختر **الجهاز**: Windows Computer
4. انسخ كلمة المرور المُنشأة (16 رقم)

#### **ج) تحديث الإعدادات:**
```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "abcd efgh ijkl mnop",
    "RecipientEmail": "<EMAIL>",
    "SenderName": "نظام إدارة الحسابات",
    "EnableSsl": true
  }
}
```

### **الخطوة 3: إعداد خدمات بريد أخرى**

#### **Outlook/Hotmail:**
```json
{
  "EmailSettings": {
    "SmtpServer": "smtp-mail.outlook.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "your-password",
    "EnableSsl": true
  }
}
```

#### **Yahoo Mail:**
```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.mail.yahoo.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "your-app-password",
    "EnableSsl": true
  }
}
```

## 📧 **مثال على رسالة البريد الإلكتروني**

ستصلك رسائل بريد إلكتروني منسقة تحتوي على:

### **الموضوع:**
```
🔔 تنبيه استحقاق حساب: استضافة موقع الشركة
```

### **المحتوى:**
- 📋 **اسم الحساب**: استضافة موقع الشركة
- 💰 **المبلغ**: 500.00 ر.س
- 📅 **تاريخ الإنشاء**: 2024/01/15
- ⏰ **تاريخ الاستحقاق**: 2025/01/15
- ⏳ **الأيام المتبقية**: 25 يوم

## 🔧 **إعدادات متقدمة**

### **تخصيص فترة الإشعار:**
```json
{
  "EmailSettings": {
    "NotificationDaysBeforeExpiry": 30
  }
}
```

### **تخصيص فترة الفحص:**
```json
{
  "EmailSettings": {
    "CheckIntervalHours": 1
  }
}
```

### **تعطيل SSL (غير مُنصح):**
```json
{
  "EmailSettings": {
    "EnableSsl": false
  }
}
```

## 🛠️ **استكشاف الأخطاء**

### **خطأ: "Authentication failed"**
**الأسباب المحتملة:**
- كلمة مرور خاطئة
- لم يتم تفعيل كلمة مرور التطبيق
- البريد الإلكتروني خاطئ

**الحل:**
1. تأكد من صحة البريد الإلكتروني
2. استخدم كلمة مرور التطبيق وليس كلمة مرور الحساب
3. تأكد من تفعيل المصادقة الثنائية

### **خطأ: "Connection failed"**
**الأسباب المحتملة:**
- خادم SMTP خاطئ
- منفذ خاطئ
- مشكلة في الاتصال بالإنترنت

**الحل:**
1. تحقق من إعدادات SMTP
2. تأكد من الاتصال بالإنترنت
3. جرب منافذ مختلفة (587, 465, 25)

### **خطأ: "SSL/TLS error"**
**الحل:**
```json
{
  "EmailSettings": {
    "EnableSsl": false
  }
}
```

## 🔒 **نصائح الأمان**

### **حماية كلمة المرور:**
- لا تشارك كلمة مرور التطبيق مع أحد
- استخدم كلمة مرور تطبيق منفصلة لكل تطبيق
- قم بحذف كلمات مرور التطبيقات غير المستخدمة

### **تشفير الملف:**
يمكنك تشفير ملف `appsettings.json` لحماية البيانات الحساسة.

## 📱 **اختبار الإعداد**

1. شغّل التطبيق
2. اضغط على زر **"📧 فحص الإشعارات"**
3. تحقق من وصول رسالة اختبار إلى بريدك الإلكتروني

## 🎯 **المميزات الجديدة**

### **رسائل منسقة:**
- تصميم HTML جميل ومنسق
- ألوان تعبر عن حالة الحساب
- جدول منظم للمعلومات

### **معلومات شاملة:**
- تفاصيل كاملة عن الحساب
- عدد الأيام المتبقية
- تاريخ ووقت الإرسال

### **إعدادات مرنة:**
- تخصيص فترة الإشعار
- تخصيص فترة الفحص
- دعم خدمات بريد متعددة

---

## 🎉 **الخلاصة**

تم تحديث النظام بنجاح ليستخدم البريد الإلكتروني بدلاً من واتساب. الآن ستحصل على:

- ✅ **إشعارات بريد إلكتروني منسقة**
- ✅ **إعدادات قابلة للتخصيص**
- ✅ **دعم خدمات بريد متعددة**
- ✅ **أمان أفضل للبيانات**

**النظام جاهز للاستخدام! 📧**
