# 📦 دليل إنشاء برنامج التنصيب - Account Hosting Manager

## 🚀 **الطرق المختلفة لإنشاء المثبت:**

### 1️⃣ **الطريقة السريعة - تشغيل تلقائي:**

```bash
# شغل هذا الأمر لإنشاء جميع أنواع المثبتات
BuildAllInstallers.bat
```

### 2️⃣ **الطريقة اليدوية - خطوة بخطوة:**

#### **أ) إنشاء ملف تنفيذي مستقل:**
```bash
# بناء التطبيق
dotnet build -c Release

# نشر التطبيق كملف واحد
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
```

#### **ب) إنشاء مثبت بسيط:**
```powershell
# تشغيل سكريبت PowerShell
.\CreateInstaller.ps1
```

#### **ج) إنشاء مثبت متقدم (Inno Setup):**
1. حمل وثبت [Inno Setup](https://jrsoftware.org/isdl.php)
2. افتح ملف `AccountHostingSetup.iss`
3. اضغط F9 أو Build → Compile

## 📁 **أنواع المثبتات المتوفرة:**

### 🎯 **1. النسخة المحمولة (Portable):**
- **الملف:** `AccountHostingManager_Portable_v1.2.0.zip`
- **المميزات:**
  - لا تحتاج تثبيت
  - يمكن تشغيلها من USB
  - حجم صغير
- **الاستخدام:**
  - فك الضغط
  - شغل `Account Hosting.exe`

### 🛠️ **2. المثبت البسيط:**
- **الملف:** `AccountHostingManager_v1.2.0.zip`
- **المميزات:**
  - تثبيت في Program Files
  - إنشاء اختصارات
  - إلغاء تثبيت سهل
- **الاستخدام:**
  - فك الضغط
  - شغل `Install.bat` كمسؤول

### 🎨 **3. المثبت المتقدم (Inno Setup):**
- **الملف:** `AccountHostingManager_Setup_v1.2.0.exe`
- **المميزات:**
  - واجهة تثبيت احترافية
  - خيارات متقدمة
  - دعم اللغة العربية
  - ربط ملفات JSON
- **الاستخدام:**
  - شغل الملف واتبع التعليمات

## 🔧 **متطلبات إنشاء المثبت:**

### **البرامج المطلوبة:**
- ✅ .NET 9 SDK
- ✅ PowerShell 5.0+ (مثبت مع Windows)
- ⚡ Inno Setup (للمثبت المتقدم - اختياري)

### **الملفات المطلوبة:**
- ✅ `CreateInstaller.ps1` - سكريبت PowerShell
- ✅ `AccountHostingSetup.iss` - سكريبت Inno Setup
- ✅ `BuildAllInstallers.bat` - سكريبت تلقائي
- ✅ ملفات التطبيق المنشورة

## 📋 **خطوات إنشاء المثبت بالتفصيل:**

### **الخطوة 1: التحضير**
```bash
# تأكد من وجود .NET SDK
dotnet --version

# تأكد من أن المشروع يبنى بنجاح
dotnet build -c Release
```

### **الخطوة 2: النشر**
```bash
# إنشاء ملف تنفيذي مستقل
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
```

### **الخطوة 3: إنشاء المثبت**
```bash
# الطريقة السريعة
BuildAllInstallers.bat

# أو الطريقة اليدوية
powershell -ExecutionPolicy Bypass -File CreateInstaller.ps1
```

## 📦 **محتويات المثبت:**

### **الملفات الأساسية:**
- `Account Hosting.exe` - التطبيق الرئيسي
- `*.dll` - مكتبات .NET المطلوبة
- `appsettings.json` - ملف الإعدادات
- `sample-data.json` - بيانات تجريبية

### **ملفات التوثيق:**
- `README.md` - دليل عام
- `USER_GUIDE.md` - دليل المستخدم
- `DEVELOPER_GUIDE.md` - دليل المطور
- `DATA_LOCATION_GUIDE.md` - دليل مكان البيانات
- `LICENSE` - ترخيص الاستخدام

### **ملفات التثبيت:**
- `Install.bat` - سكريبت التثبيت
- `Uninstall.bat` - سكريبت إلغاء التثبيت
- `README.txt` - تعليمات التثبيت

## 🎯 **خيارات التخصيص:**

### **تغيير معلومات التطبيق:**
```powershell
# في CreateInstaller.ps1
$AppName = "اسم التطبيق الجديد"
$Version = "2.0.0"
```

### **تغيير مكان التثبيت:**
```batch
# في Install.bat
set "INSTALL_DIR=%ProgramFiles%\مجلد جديد"
```

### **إضافة ملفات إضافية:**
```powershell
# في CreateInstaller.ps1
Copy-Item "ملف إضافي.txt" -Destination $InstallerDir
```

## 🚀 **التوزيع:**

### **رفع على GitHub Releases:**
1. إنشاء Release جديد
2. رفع ملفات ZIP
3. كتابة Release Notes

### **التوزيع المحلي:**
1. نسخ الملفات على USB
2. مشاركة عبر الشبكة المحلية
3. رفع على خادم FTP

## ⚠️ **نصائح مهمة:**

### **الأمان:**
- ✅ فحص الملفات بمكافح الفيروسات
- ✅ توقيع رقمي للملفات (للتوزيع التجاري)
- ✅ اختبار المثبت على أجهزة مختلفة

### **الاختبار:**
- ✅ اختبار التثبيت على Windows 10/11
- ✅ اختبار إلغاء التثبيت
- ✅ اختبار النسخة المحمولة
- ✅ التأكد من عمل الاختصارات

### **التحديثات:**
- ✅ تحديث رقم الإصدار في جميع الملفات
- ✅ تحديث CHANGELOG.md
- ✅ اختبار التحديث من إصدار سابق

## 🔍 **استكشاف الأخطاء:**

### **خطأ: "dotnet command not found"**
**الحل:** ثبت .NET 9 SDK من الموقع الرسمي

### **خطأ: "PowerShell execution policy"**
**الحل:** 
```powershell
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope CurrentUser
```

### **خطأ: "Access denied during installation"**
**الحل:** شغل Install.bat كمسؤول

### **خطأ: "Application won't start"**
**الحل:** تأكد من وجود .NET 9 Runtime على الجهاز المستهدف

---

## 🎉 **الخلاصة:**

بعد تشغيل `BuildAllInstallers.bat` ستحصل على:
- 📦 نسخة محمولة (Portable)
- 🛠️ مثبت بسيط مع اختصارات
- 📋 ملفات توثيق شاملة
- 🎯 ملفات جاهزة للتوزيع

**المثبت جاهز للاستخدام والتوزيع! 🚀**
