@echo off
title تثبيت Account Hosting Manager
chcp 65001 >nul
echo.
echo ================================================
echo           تثبيت Account Hosting Manager v1.2.0
echo ================================================
echo.

REM تحديد مجلد التثبيت
set "INSTALL_DIR=%ProgramFiles%\AccountHosting"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Account Hosting Manager.lnk"
set "START_MENU_SHORTCUT=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Account Hosting Manager.lnk"

echo 📁 إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 📋 نسخ ملفات التطبيق...
copy /Y "Account Hosting.exe" "%INSTALL_DIR%\" >nul
copy /Y "*.dll" "%INSTALL_DIR%\" >nul 2>nul
copy /Y "*.pdb" "%INSTALL_DIR%\" >nul 2>nul
copy /Y "appsettings.json" "%INSTALL_DIR%\" >nul 2>nul
copy /Y "sample-data.json" "%INSTALL_DIR%\" >nul 2>nul
copy /Y "*.md" "%INSTALL_DIR%\" >nul 2>nul
copy /Y "LICENSE" "%INSTALL_DIR%\" >nul 2>nul

echo 🔗 إنشاء اختصارات...
REM إنشاء اختصار سطح المكتب
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\Account Hosting.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Account Hosting Manager - إدارة الحسابات'; $Shortcut.Save()}"

REM إنشاء اختصار قائمة ابدأ
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\Account Hosting.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Account Hosting Manager - إدارة الحسابات'; $Shortcut.Save()}"

echo.
echo ✅ تم التثبيت بنجاح!
echo.
echo 📍 مكان التثبيت: %INSTALL_DIR%
echo 🖥️  اختصار سطح المكتب: تم إنشاؤه
echo 📋 قائمة ابدأ: تم إنشاؤه
echo.
echo 💾 البيانات ستُحفظ في: %%AppData%%\AccountHosting
echo.

set /p run=هل تريد تشغيل التطبيق الآن؟ (Y/N): 
if /i "%run%"=="Y" start "" "%INSTALL_DIR%\Account Hosting.exe"

echo.
echo اضغط أي مفتاح لإغلاق النافذة...
pause >nul
