@echo off
title إلغاء تثبيت Account Hosting Manager
chcp 65001 >nul
echo.
echo ================================================
echo         إلغاء تثبيت Account Hosting Manager v1.2.0
echo ================================================
echo.

set "INSTALL_DIR=%ProgramFiles%\AccountHosting"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Account Hosting Manager.lnk"
set "START_MENU_SHORTCUT=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Account Hosting Manager.lnk"

echo ⚠️  هذا سيقوم بحذف التطبيق نهائياً
echo    (البيانات في %%AppData%% ستبقى محفوظة)
echo.
set /p confirm=هل أنت متأكد؟ (Y/N): 
if /i not "%confirm%"=="Y" goto :cancel

echo 🗑️  حذف ملفات التطبيق...
if exist "%INSTALL_DIR%" rmdir /s /q "%INSTALL_DIR%"

echo 🔗 حذف الاختصارات...
if exist "%DESKTOP_SHORTCUT%" del "%DESKTOP_SHORTCUT%"
if exist "%START_MENU_SHORTCUT%" del "%START_MENU_SHORTCUT%"

echo.
echo ✅ تم إلغاء التثبيت بنجاح!
echo    البيانات محفوظة في: %%AppData%%\AccountHosting
echo.
goto :end

:cancel
echo ❌ تم إلغاء العملية
echo.

:end
echo اضغط أي مفتاح لإغلاق النافذة...
pause >nul
