# 🔄 دليل الترقية من واتساب إلى البريد الإلكتروني

## 📋 **نظرة عامة**

تم تحديث التطبيق ليستخدم **البريد الإلكتروني** بدلاً من **واتساب** لإرسال الإشعارات. هذا الدليل يوضح كيفية الترقية للمستخدمين الحاليين.

## ✨ **المميزات الجديدة**

### **🎨 رسائل منسقة:**
- تصميم HTML جميل ومنسق
- ألوان تعبر عن حالة الحساب
- جدول منظم للمعلومات

### **⚙️ إعدادات مرنة:**
- تخصيص فترة الإشعار
- تخصيص فترة الفحص
- دعم خدمات بريد متعددة

### **🔒 أمان أفضل:**
- لا حاجة لـ API خارجي
- بيانات محفوظة محلياً
- تشفير اتصال البريد الإلكتروني

## 🔄 **خطوات الترقية**

### **الخطوة 1: النسخ الاحتياطي**
```bash
# انسخ ملف البيانات الحالي
copy "%AppData%\AccountHosting\accounts.json" "%AppData%\AccountHosting\accounts_backup.json"
```

### **الخطوة 2: تحديث التطبيق**
1. حمّل الإصدار الجديد
2. استبدل الملفات القديمة
3. شغّل التطبيق الجديد

### **الخطوة 3: إعداد البريد الإلكتروني**
1. افتح ملف `appsettings.json`
2. حدث إعدادات البريد الإلكتروني:

```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "your-app-password",
    "RecipientEmail": "<EMAIL>",
    "SenderName": "نظام إدارة الحسابات",
    "EnableSsl": true,
    "NotificationDaysBeforeExpiry": 30,
    "CheckIntervalHours": 1
  }
}
```

### **الخطوة 4: إعداد Gmail**
1. فعّل المصادقة الثنائية
2. أنشئ كلمة مرور تطبيق
3. استخدم كلمة مرور التطبيق في الإعدادات

## 📊 **التوافق مع البيانات القديمة**

### **الحقول المحدثة:**
- `WhatsAppNotificationSent` → `EmailNotificationSent`
- التطبيق يدعم الحقلين للتوافق مع الإصدارات السابقة

### **لا حاجة لتعديل البيانات:**
- ملف `accounts.json` الحالي سيعمل بدون تعديل
- الحسابات الموجودة ستظهر بنفس الحالة
- الإشعارات المرسلة سابقاً ستبقى محفوظة

## 🔧 **الإعدادات المتقدمة**

### **تخصيص فترة الإشعار:**
```json
{
  "EmailSettings": {
    "NotificationDaysBeforeExpiry": 45
  }
}
```

### **تخصيص فترة الفحص:**
```json
{
  "EmailSettings": {
    "CheckIntervalHours": 2
  }
}
```

### **استخدام خدمة بريد أخرى:**

#### **Outlook:**
```json
{
  "EmailSettings": {
    "SmtpServer": "smtp-mail.outlook.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>"
  }
}
```

#### **Yahoo:**
```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.mail.yahoo.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>"
  }
}
```

## 🧪 **اختبار الإعداد**

### **اختبار سريع:**
1. شغّل التطبيق
2. اضغط على زر **"📧 فحص الإشعارات"**
3. تحقق من وصول رسالة إلى بريدك الإلكتروني

### **اختبار شامل:**
1. أضف حساب جديد بتاريخ استحقاق خلال 25 يوم
2. اضغط على زر فحص الإشعارات
3. تأكد من وصول رسالة منسقة

## ❌ **إزالة الملفات القديمة**

بعد التأكد من عمل النظام الجديد، يمكنك حذف:
- `WhatsAppNotificationService.cs` (إذا كان موجوداً)
- أي إعدادات واتساب قديمة

## 🆘 **استكشاف الأخطاء**

### **خطأ: "Authentication failed"**
**الحل:**
1. تأكد من استخدام كلمة مرور التطبيق
2. تحقق من تفعيل المصادقة الثنائية
3. تأكد من صحة البريد الإلكتروني

### **خطأ: "Connection timeout"**
**الحل:**
1. تحقق من إعدادات SMTP
2. جرب منافذ مختلفة (587, 465, 25)
3. تأكد من الاتصال بالإنترنت

### **لا تصل رسائل:**
**الحل:**
1. تحقق من مجلد الرسائل المهملة
2. تأكد من صحة البريد المستقبل
3. جرب إرسال رسالة اختبار

## 📞 **الدعم**

### **للمساعدة:**
- راجع ملف `EMAIL_SETUP_GUIDE.md`
- تحقق من ملف `README.md` المحدث
- افتح Issue في المستودع

### **الإبلاغ عن مشاكل:**
- وصف المشكلة بالتفصيل
- أرفق لقطة شاشة من رسالة الخطأ
- اذكر نوع خدمة البريد المستخدمة

## 🎉 **الخلاصة**

### **المميزات الجديدة:**
- ✅ رسائل بريد إلكتروني منسقة
- ✅ إعدادات قابلة للتخصيص
- ✅ دعم خدمات بريد متعددة
- ✅ أمان أفضل للبيانات
- ✅ توافق مع البيانات القديمة

### **خطوات بسيطة:**
1. حدّث التطبيق
2. أعدّ البريد الإلكتروني
3. اختبر الإعداد
4. استمتع بالمميزات الجديدة

**الترقية مكتملة! 📧✨**
