﻿<Window x:Class="Account_Hosting.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Account_Hosting"
        mc:Ignorable="d"
        Title="إدارة الحسابات - Account Hosting Manager"
        Height="700"
        Width="1200"
        MinHeight="500"
        MinWidth="800"
        WindowStartupLocation="CenterScreen"
        Background="#F8F9FA"
        FlowDirection="RightToLeft">

        <Window.Resources>
                <!-- أنماط Flat UI -->
                <Style x:Key="HeaderStyle"
                       TargetType="TextBlock">
                        <Setter Property="FontSize"
                                Value="24"/>
                        <Setter Property="FontWeight"
                                Value="Bold"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="HorizontalAlignment"
                                Value="Center"/>
                        <Setter Property="VerticalAlignment"
                                Value="Center"/>
                </Style>

                <Style x:Key="FlatButtonStyle"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="#007ACC"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="25,15"/>
                        <Setter Property="Margin"
                                Value="8"/>
                        <Setter Property="FontSize"
                                Value="15"/>
                        <Setter Property="FontWeight"
                                Value="SemiBold"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="MinWidth"
                                Value="140"/>
                        <Setter Property="MinHeight"
                                Value="45"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border x:Name="PART_Border"
                                                        Background="{TemplateBinding Background}"
                                                        CornerRadius="8"
                                                        BorderThickness="0">
                                                        <Border.Effect>
                                                                <DropShadowEffect Color="#40000000"
                                                                                  Direction="270"
                                                                                  ShadowDepth="2"
                                                                                  BlurRadius="4"
                                                                                  Opacity="0.3"/>
                                                        </Border.Effect>
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"
                                                                          Margin="{TemplateBinding Padding}"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#005A9E"/>
                                                                <Setter TargetName="PART_Border"
                                                                        Property="Effect">
                                                                        <Setter.Value>
                                                                                <DropShadowEffect Color="#40000000"
                                                                                                  Direction="270"
                                                                                                  ShadowDepth="3"
                                                                                                  BlurRadius="6"
                                                                                                  Opacity="0.4"/>
                                                                        </Setter.Value>
                                                                </Setter>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#004578"/>
                                                                <Setter TargetName="PART_Border"
                                                                        Property="Effect">
                                                                        <Setter.Value>
                                                                                <DropShadowEffect Color="#40000000"
                                                                                                  Direction="270"
                                                                                                  ShadowDepth="1"
                                                                                                  BlurRadius="2"
                                                                                                  Opacity="0.5"/>
                                                                        </Setter.Value>
                                                                </Setter>
                                                        </Trigger>
                                                        <Trigger Property="IsEnabled"
                                                                 Value="False">
                                                                <Setter Property="Background"
                                                                        Value="#ADB5BD"/>
                                                                <Setter Property="Opacity"
                                                                        Value="0.6"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <Style x:Key="AddButtonStyle"
                       TargetType="Button"
                       BasedOn="{StaticResource FlatButtonStyle}">
                        <Setter Property="Background"
                                Value="#28A745"/>
                        <Setter Property="Padding"
                                Value="30,18"/>
                        <Setter Property="FontSize"
                                Value="16"/>
                        <Setter Property="MinWidth"
                                Value="160"/>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#218838"/>
                                </Trigger>
                                <Trigger Property="IsPressed"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#1E7E34"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="EditButtonStyle"
                       TargetType="Button"
                       BasedOn="{StaticResource FlatButtonStyle}">
                        <Setter Property="Background"
                                Value="#FFC107"/>
                        <Setter Property="Foreground"
                                Value="#212529"/>
                        <Setter Property="Padding"
                                Value="28,18"/>
                        <Setter Property="FontSize"
                                Value="16"/>
                        <Setter Property="MinWidth"
                                Value="150"/>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#E0A800"/>
                                </Trigger>
                                <Trigger Property="IsPressed"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#D39E00"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="DeleteButtonStyle"
                       TargetType="Button"
                       BasedOn="{StaticResource FlatButtonStyle}">
                        <Setter Property="Background"
                                Value="#DC3545"/>
                        <Setter Property="Padding"
                                Value="28,18"/>
                        <Setter Property="FontSize"
                                Value="16"/>
                        <Setter Property="MinWidth"
                                Value="150"/>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#C82333"/>
                                </Trigger>
                                <Trigger Property="IsPressed"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#BD2130"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="RefreshButtonStyle"
                       TargetType="Button"
                       BasedOn="{StaticResource FlatButtonStyle}">
                        <Setter Property="Background"
                                Value="#17A2B8"/>
                        <Setter Property="Padding"
                                Value="28,18"/>
                        <Setter Property="FontSize"
                                Value="16"/>
                        <Setter Property="MinWidth"
                                Value="160"/>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#138496"/>
                                </Trigger>
                                <Trigger Property="IsPressed"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#117A8B"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="DataGridStyle"
                       TargetType="DataGrid">
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="BorderBrush"
                                Value="#DEE2E6"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="GridLinesVisibility"
                                Value="Horizontal"/>
                        <Setter Property="HorizontalGridLinesBrush"
                                Value="#F1F3F4"/>
                        <Setter Property="RowBackground"
                                Value="White"/>
                        <Setter Property="AlternatingRowBackground"
                                Value="#F8F9FA"/>
                        <Setter Property="HeadersVisibility"
                                Value="Column"/>
                        <Setter Property="AutoGenerateColumns"
                                Value="False"/>
                        <Setter Property="CanUserAddRows"
                                Value="False"/>
                        <Setter Property="CanUserDeleteRows"
                                Value="False"/>
                        <Setter Property="SelectionMode"
                                Value="Single"/>
                        <Setter Property="SelectionUnit"
                                Value="FullRow"/>
                        <Setter Property="FontSize"
                                Value="13"/>
                </Style>

                <Style x:Key="DataGridColumnHeaderStyle"
                       TargetType="DataGridColumnHeader">
                        <Setter Property="Background"
                                Value="#495057"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="FontWeight"
                                Value="SemiBold"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="Padding"
                                Value="10,8"/>
                        <Setter Property="HorizontalContentAlignment"
                                Value="Center"/>
                </Style>

                <Style x:Key="StatusBarStyle"
                       TargetType="StatusBar">
                        <Setter Property="Background"
                                Value="#495057"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="FontSize"
                                Value="12"/>
                </Style>
        </Window.Resources>

        <Grid>
                <Grid.RowDefinitions>
                        <RowDefinition Height="60"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- شريط العنوان -->
                <Border Grid.Row="0"
                        Background="#343A40">
                        <Grid>
                                <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0"
                                           Text="🏢 إدارة الحسابات - Account Hosting Manager"
                                           Style="{StaticResource HeaderStyle}"/>

                                <StackPanel Grid.Column="1"
                                            Orientation="Horizontal"
                                            VerticalAlignment="Center"
                                            Margin="0,0,20,0"
                                            FlowDirection="LeftToRight">
                                        <Button Content="📧 فحص الإشعارات"
                                                Style="{StaticResource RefreshButtonStyle}"
                                                Command="{Binding CheckNotificationsCommand}"
                                                ToolTip="فحص وإرسال إشعارات البريد الإلكتروني للحسابات المستحقة"
                                                Margin="1"/>
                                        <Button Content="📁 مكان البيانات"
                                                Style="{StaticResource FlatButtonStyle}"
                                                Command="{Binding ShowDataLocationCommand}"
                                                ToolTip="عرض مكان حفظ ملف البيانات"
                                                Margin="5"/>
                                </StackPanel>
                        </Grid>
                </Border>

                <!-- شريط الأدوات -->
                <Border Grid.Row="1"
                        Background="White"
                        BorderBrush="#DEE2E6"
                        BorderThickness="0,0,0,1"
                        Padding="20,15">
                        <StackPanel Orientation="Horizontal"
                                    FlowDirection="RightToLeft"
                                    HorizontalAlignment="Right">
                                <Button Content="🔄 تحديث البيانات"
                                        Style="{StaticResource RefreshButtonStyle}"
                                        Command="{Binding RefreshCommand}"
                                        ToolTip="إعادة تحميل قائمة الحسابات من الملف"/>

                                <Separator Width="20"
                                           Background="Transparent"/>

                                <Button Content="🗑️ حذف الحساب"
                                        Style="{StaticResource DeleteButtonStyle}"
                                        Command="{Binding DeleteAccountCommand}"
                                        ToolTip="حذف الحساب المحدد نهائياً"/>

                                <Button Content="✏️ تعديل الحساب"
                                        Style="{StaticResource EditButtonStyle}"
                                        Command="{Binding EditAccountCommand}"
                                        ToolTip="تعديل بيانات الحساب المحدد"/>

                                <Button Content="📝 إضافة حساب جديد"
                                        Style="{StaticResource AddButtonStyle}"
                                        Command="{Binding AddAccountCommand}"
                                        ToolTip="إضافة حساب جديد مع جميع التفاصيل"/>
                        </StackPanel>
                </Border>

                <!-- جدول البيانات -->
                <Border Grid.Row="2"
                        Background="White"
                        BorderBrush="#DEE2E6"
                        BorderThickness="1"
                        Margin="20">
                        <Grid>
                                <!-- مؤشر التحميل -->
                                <Border Background="White"
                                        Visibility="{Binding IsLoading, Converter={x:Static local:BooleanToVisibilityConverter.Instance}}"
                                        Panel.ZIndex="1">
                                        <StackPanel HorizontalAlignment="Center"
                                                    VerticalAlignment="Center">
                                                <TextBlock Text="⏳ جاري التحميل..."
                                                           FontSize="16"
                                                           Foreground="#6C757D"
                                                           HorizontalAlignment="Center"/>
                                        </StackPanel>
                                </Border>

                                <!-- جدول البيانات -->
                                <DataGrid x:Name="AccountsDataGrid"
                                          Style="{StaticResource DataGridStyle}"
                                          ItemsSource="{Binding Accounts}"
                                          SelectedItem="{Binding SelectedAccount}"
                                          ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                                          FlowDirection="RightToLeft">

                                        <DataGrid.Columns>
                                                <DataGridTextColumn Header="المعرف"
                                                                    Binding="{Binding Id}"
                                                                    Width="80"
                                                                    IsReadOnly="True">
                                                        <DataGridTextColumn.ElementStyle>
                                                                <Style TargetType="TextBlock">
                                                                        <Setter Property="HorizontalAlignment"
                                                                                Value="Center"/>
                                                                        <Setter Property="VerticalAlignment"
                                                                                Value="Center"/>
                                                                        <Setter Property="Padding"
                                                                                Value="5"/>
                                                                </Style>
                                                        </DataGridTextColumn.ElementStyle>
                                                </DataGridTextColumn>

                                                <DataGridTextColumn Header="اسم الحساب"
                                                                    Binding="{Binding AccountName}"
                                                                    Width="*"
                                                                    IsReadOnly="True">
                                                        <DataGridTextColumn.ElementStyle>
                                                                <Style TargetType="TextBlock">
                                                                        <Setter Property="HorizontalAlignment"
                                                                                Value="Center"/>
                                                                        <Setter Property="VerticalAlignment"
                                                                                Value="Center"/>
                                                                        <Setter Property="Padding"
                                                                                Value="10,5"/>
                                                                        <Setter Property="FontWeight"
                                                                                Value="SemiBold"/>
                                                                        <Setter Property="TextAlignment"
                                                                                Value="Center"/>
                                                                </Style>
                                                        </DataGridTextColumn.ElementStyle>
                                                </DataGridTextColumn>

                                                <DataGridTextColumn Header="السعر"
                                                                    Binding="{Binding Price, StringFormat=C}"
                                                                    Width="120"
                                                                    IsReadOnly="True">
                                                        <DataGridTextColumn.ElementStyle>
                                                                <Style TargetType="TextBlock">
                                                                        <Setter Property="HorizontalAlignment"
                                                                                Value="Center"/>
                                                                        <Setter Property="VerticalAlignment"
                                                                                Value="Center"/>
                                                                        <Setter Property="Padding"
                                                                                Value="5"/>
                                                                        <Setter Property="FontWeight"
                                                                                Value="Bold"/>
                                                                        <Setter Property="Foreground"
                                                                                Value="#28A745"/>
                                                                </Style>
                                                        </DataGridTextColumn.ElementStyle>
                                                </DataGridTextColumn>

                                                <DataGridTextColumn Header="تاريخ الإنشاء"
                                                                    Binding="{Binding CreatedDate, StringFormat=yyyy/MM/dd}"
                                                                    Width="120"
                                                                    IsReadOnly="True">
                                                        <DataGridTextColumn.ElementStyle>
                                                                <Style TargetType="TextBlock">
                                                                        <Setter Property="HorizontalAlignment"
                                                                                Value="Center"/>
                                                                        <Setter Property="VerticalAlignment"
                                                                                Value="Center"/>
                                                                        <Setter Property="Padding"
                                                                                Value="5"/>
                                                                </Style>
                                                        </DataGridTextColumn.ElementStyle>
                                                </DataGridTextColumn>

                                                <DataGridTextColumn Header="تاريخ الاستحقاق"
                                                                    Binding="{Binding DueDate, StringFormat=yyyy/MM/dd}"
                                                                    Width="120"
                                                                    IsReadOnly="True">
                                                        <DataGridTextColumn.ElementStyle>
                                                                <Style TargetType="TextBlock">
                                                                        <Setter Property="HorizontalAlignment"
                                                                                Value="Center"/>
                                                                        <Setter Property="VerticalAlignment"
                                                                                Value="Center"/>
                                                                        <Setter Property="Padding"
                                                                                Value="5"/>
                                                                </Style>
                                                        </DataGridTextColumn.ElementStyle>
                                                </DataGridTextColumn>

                                                <DataGridTemplateColumn Header="الحالة"
                                                                        Width="100">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                                <DataTemplate>
                                                                        <Border CornerRadius="12"
                                                                                Padding="8,4"
                                                                                HorizontalAlignment="Center">
                                                                                <Border.Style>
                                                                                        <Style TargetType="Border">
                                                                                                <Style.Triggers>
                                                                                                        <DataTrigger Binding="{Binding IsExpiringSoon}"
                                                                                                                     Value="True">
                                                                                                                <Setter Property="Background"
                                                                                                                        Value="#FFF3CD"/>
                                                                                                        </DataTrigger>
                                                                                                        <DataTrigger Binding="{Binding IsExpired}"
                                                                                                                     Value="True">
                                                                                                                <Setter Property="Background"
                                                                                                                        Value="#F8D7DA"/>
                                                                                                        </DataTrigger>
                                                                                                        <DataTrigger Binding="{Binding IsActive}"
                                                                                                                     Value="True">
                                                                                                                <Setter Property="Background"
                                                                                                                        Value="#D4EDDA"/>
                                                                                                        </DataTrigger>
                                                                                                        <DataTrigger Binding="{Binding IsDisabled}"
                                                                                                                     Value="True">
                                                                                                                <Setter Property="Background"
                                                                                                                        Value="#E2E3E5"/>
                                                                                                        </DataTrigger>
                                                                                                </Style.Triggers>
                                                                                        </Style>
                                                                                </Border.Style>
                                                                                <TextBlock HorizontalAlignment="Center"
                                                                                           VerticalAlignment="Center"
                                                                                           FontSize="11"
                                                                                           FontWeight="SemiBold">
                                                                                        <TextBlock.Style>
                                                                                                <Style TargetType="TextBlock">
                                                                                                        <Style.Triggers>
                                                                                                                <DataTrigger Binding="{Binding IsExpiringSoon}"
                                                                                                                             Value="True">
                                                                                                                        <Setter Property="Text"
                                                                                                                                Value="⚠️ قريب"/>
                                                                                                                        <Setter Property="Foreground"
                                                                                                                                Value="#856404"/>
                                                                                                                </DataTrigger>
                                                                                                                <DataTrigger Binding="{Binding IsExpired}"
                                                                                                                             Value="True">
                                                                                                                        <Setter Property="Text"
                                                                                                                                Value="❌ منتهي"/>
                                                                                                                        <Setter Property="Foreground"
                                                                                                                                Value="#721C24"/>
                                                                                                                </DataTrigger>
                                                                                                                <DataTrigger Binding="{Binding IsActive}"
                                                                                                                             Value="True">
                                                                                                                        <Setter Property="Text"
                                                                                                                                Value="✅ نشط"/>
                                                                                                                        <Setter Property="Foreground"
                                                                                                                                Value="#155724"/>
                                                                                                                </DataTrigger>
                                                                                                                <DataTrigger Binding="{Binding IsDisabled}"
                                                                                                                             Value="True">
                                                                                                                        <Setter Property="Text"
                                                                                                                                Value="⏸️ معطل"/>
                                                                                                                        <Setter Property="Foreground"
                                                                                                                                Value="#6C757D"/>
                                                                                                                </DataTrigger>
                                                                                                        </Style.Triggers>
                                                                                                </Style>
                                                                                        </TextBlock.Style>
                                                                                </TextBlock>
                                                                        </Border>
                                                                </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                </DataGridTemplateColumn>
                                        </DataGrid.Columns>
                                </DataGrid>
                        </Grid>
                </Border>

                <!-- شريط الحالة -->
                <StatusBar Grid.Row="3"
                           Style="{StaticResource StatusBarStyle}"
                           FlowDirection="RightToLeft">
                        <StatusBarItem>
                                <TextBlock Text="{Binding StatusMessage}"
                                           Margin="10,5"
                                           FontWeight="SemiBold"/>
                        </StatusBarItem>
                </StatusBar>
        </Grid>
</Window>
