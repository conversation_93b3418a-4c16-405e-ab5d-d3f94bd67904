using System;

namespace Account_Hosting.Models
{
    /// <summary>
    /// يمثل نموذج الحساب الذي يحتوي على المعلومات الأساسية للحساب
    /// </summary>
    public class Account
    {
        /// <summary>
        /// معرف فريد للحساب
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// اسم الحساب
        /// </summary>
        public string AccountName { get; set; } = string.Empty;

        /// <summary>
        /// قيمة الحساب
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// تاريخ إنشاء الحساب (قابل للتعديل)
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ استحقاق الحساب (يتم حسابه تلقائياً بعد سنة من تاريخ الإنشاء)
        /// </summary>
        public DateTime DueDate { get; set; }

        /// <summary>
        /// هل تم إرسال إشعار بريد إلكتروني لهذا الحساب بالفعل
        /// </summary>
        public bool EmailNotificationSent { get; set; } = false;

        /// <summary>
        /// خاصية للتوافق مع الإصدارات السابقة (واتساب)
        /// </summary>
        public bool WhatsAppNotificationSent
        {
            get => EmailNotificationSent;
            set => EmailNotificationSent = value;
        }

        /// <summary>
        /// هل الحساب نشط أم لا (يمكن تعطيله يدوياً)
        /// </summary>
        public bool IsAccountActive { get; set; } = true;

        /// <summary>
        /// هل الحساب نشط (لم ينته بعد ومفعل)
        /// </summary>
        public bool IsActive => IsAccountActive && DateTime.Now < DueDate && !IsExpiringSoon;

        /// <summary>
        /// هل الحساب منتهي الصلاحية
        /// </summary>
        public bool IsExpired => DateTime.Now >= DueDate;

        /// <summary>
        /// هل الحساب قريب من الانتهاء (خلال 30 يوم)
        /// </summary>
        public bool IsExpiringSoon => IsAccountActive && !IsExpired && (DueDate - DateTime.Now).Days <= 30;

        /// <summary>
        /// هل الحساب معطل يدوياً
        /// </summary>
        public bool IsDisabled => !IsAccountActive;

        /// <summary>
        /// عدد الأيام المتبقية حتى الانتهاء
        /// </summary>
        public int DaysUntilExpiry => (DueDate - DateTime.Now).Days;
    }
}