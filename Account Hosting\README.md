# 🏢 إدارة الحسابات - Account Hosting Manager

برنامج WPF بلغة C# لإدارة الحسابات مع تصميم Flat UI عصري وإشعارات بريد إلكتروني تلقائية.

## ✨ المميزات

- **واجهة عصرية**: تصميم Flat UI بألوان هادئة وأيقونات حديثة
- **إدارة شاملة**: إضافة، تعديل، وحذف الحسابات بسهولة
- **إشعارات تلقائية**: إرسال تنبيهات بريد إلكتروني قبل شهر من انتهاء الصلاحية
- **تخزين محلي**: حفظ البيانات في ملف JSON (لا يتطلب قاعدة بيانات)
- **مراقبة مستمرة**: فحص تلقائي للحسابات كل ساعة
- **حالات بصرية**: عرض حالة كل حساب (نشط، قريب الانتهاء، منتهي)

## 🚀 المتطلبات

- .NET 9.0 أو أحدث
- Windows 10/11
- Visual Studio 2022 أو أحدث

## 📦 التثبيت والتشغيل

1. **استنساخ المشروع**:

   ```bash
   git clone [repository-url]
   cd "Account Hosting"
   ```

2. **بناء المشروع**:

   ```bash
   dotnet build
   ```

3. **تشغيل التطبيق**:
   ```bash
   dotnet run
   ```

## 📧 إعداد إشعارات البريد الإلكتروني

لتفعيل إشعارات البريد الإلكتروني، يجب تحديث الإعدادات في ملف `appsettings.json`:

### 1. تحديث إعدادات البريد الإلكتروني

```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "your-app-password",
    "RecipientEmail": "<EMAIL>",
    "SenderName": "نظام إدارة الحسابات",
    "EnableSsl": true
  }
}
```

### 2. إعداد Gmail (الأكثر شيوعاً)

#### أ) تفعيل المصادقة الثنائية:

1. اذهب إلى [حساب Google](https://myaccount.google.com/)
2. اختر **الأمان** من القائمة الجانبية
3. فعّل **التحقق بخطوتين**

#### ب) إنشاء كلمة مرور التطبيق:

1. في صفحة الأمان، اختر **كلمات مرور التطبيقات**
2. اختر **التطبيق**: بريد
3. اختر **الجهاز**: Windows Computer
4. انسخ كلمة المرور المُنشأة (16 رقم)

### 3. مثال على رسالة البريد الإلكتروني

ستصلك رسائل بريد إلكتروني منسقة تحتوي على:

**الموضوع:**

```
🔔 تنبيه استحقاق حساب: استضافة موقع الشركة
```

**المحتوى:**

- 📋 اسم الحساب: استضافة موقع الشركة
- 💰 المبلغ: 500.00 ر.س
- 📅 تاريخ الاستحقاق: 2025/01/15
- ⏰ المتبقي: 25 يوم

> 📖 **للمزيد من التفاصيل**: راجع ملف `EMAIL_SETUP_GUIDE.md`

## 📁 هيكل المشروع

```
Account Hosting/
├── Models/
│   └── Account.cs                 # نموذج بيانات الحساب
├── ViewModels/
│   ├── BaseViewModel.cs           # ViewModel أساسي
│   └── MainViewModel.cs           # ViewModel الرئيسي
├── Views/
│   ├── AccountDialog.xaml         # نافذة إضافة/تعديل الحساب
│   └── AccountDialog.xaml.cs
├── Services/
│   ├── DataService.cs             # خدمة إدارة البيانات JSON
│   ├── EmailNotificationService.cs    # خدمة إشعارات البريد الإلكتروني
│   ├── ConfigurationService.cs   # خدمة إدارة الإعدادات
│   └── NotificationSchedulerService.cs # خدمة المراقبة التلقائية
├── Commands/
│   └── RelayCommand.cs            # تنفيذ ICommand
├── Converters/
│   └── BooleanToVisibilityConverter.cs # محول Boolean إلى Visibility
├── MainWindow.xaml                # النافذة الرئيسية
├── MainWindow.xaml.cs
├── App.xaml
├── App.xaml.cs
└── README.md
```

## 💾 تخزين البيانات

البيانات تُحفظ في ملف JSON في المسار:

```
%AppData%/AccountHosting/accounts.json
```

مثال على هيكل البيانات:

```json
[
  {
    "Id": 1,
    "AccountName": "استضافة موقع الشركة",
    "Price": 500.0,
    "CreatedDate": "2024-01-15T10:30:00",
    "DueDate": "2025-01-15T10:30:00",
    "WhatsAppNotificationSent": false
  }
]
```

## 🎨 تخصيص التصميم

يمكن تخصيص ألوان وأنماط التصميم من خلال تعديل الـ Resources في `MainWindow.xaml`:

- **الألوان الأساسية**: `#007ACC` (أزرق)
- **ألوان الحالة**:
  - نشط: `#28A745` (أخضر)
  - تحذير: `#FFC107` (أصفر)
  - خطر: `#DC3545` (أحمر)
- **الخلفية**: `#F8F9FA` (رمادي فاتح)

## 📧 آلية الإشعارات

1. **الفحص التلقائي**: كل ساعة يتم فحص جميع الحسابات (قابل للتخصيص)
2. **شرط الإرسال**: قبل 30 يوم من تاريخ الانتهاء (قابل للتخصيص)
3. **منع التكرار**: لا يتم إرسال إشعار مرة أخرى للحساب نفسه
4. **الفحص اليدوي**: يمكن الضغط على زر "📧 فحص الإشعارات" للفحص الفوري
5. **رسائل منسقة**: رسائل HTML جميلة مع ألوان تعبر عن حالة الحساب

## 🛠️ التطوير والمساهمة

المشروع يستخدم نمط MVVM ومكتوب بطريقة نظيفة مع تعليقات شاملة. لإضافة مميزات جديدة:

1. إنشاء فرع جديد
2. إضافة الكود مع التعليقات
3. اختبار الوظائف الجديدة
4. إرسال Pull Request

## 📝 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## 📞 الدعم

لأي استفسارات أو مشاكل، يرجى فتح Issue في المستودع أو التواصل مع فريق التطوير.
