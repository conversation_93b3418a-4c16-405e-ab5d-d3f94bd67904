# 🏢 إدارة الحسابات - Account Hosting Manager

برنامج WPF بلغة C# لإدارة الحسابات مع تصميم Flat UI عصري وإشعارات واتساب تلقائية.

## ✨ المميزات

- **واجهة عصرية**: تصميم Flat UI بألوان هادئة وأيقونات حديثة
- **إدارة شاملة**: إضافة، تعديل، وحذف الحسابات بسهولة
- **إشعارات تلقائية**: إرسال تنبيهات واتساب قبل شهر من انتهاء الصلاحية
- **تخزين محلي**: حفظ البيانات في ملف JSON (لا يتطلب قاعدة بيانات)
- **مراقبة مستمرة**: فحص تلقائي للحسابات كل ساعة
- **حالات بصرية**: عرض حالة كل حساب (نشط، قريب الانتهاء، منتهي)

## 🚀 المتطلبات

- .NET 9.0 أو أحدث
- Windows 10/11
- Visual Studio 2022 أو أحدث

## 📦 التثبيت والتشغيل

1. **استنساخ المشروع**:
   ```bash
   git clone [repository-url]
   cd "Account Hosting"
   ```

2. **بناء المشروع**:
   ```bash
   dotnet build
   ```

3. **تشغيل التطبيق**:
   ```bash
   dotnet run
   ```

## 🔧 إعداد إشعارات واتساب

لتفعيل إشعارات واتساب، يجب تحديث الإعدادات في ملف `Services/WhatsAppNotificationService.cs`:

### 1. تحديث معلومات API

```csharp
private const string WHATSAPP_API_URL = "https://your-whatsapp-api-endpoint.com";
private const string PHONE_NUMBER = "+966XXXXXXXXX"; // رقم الهاتف المستقبل
```

### 2. مثال أمر cURL للإرسال

#### استخدام Twilio API:
```bash
curl -X POST "https://api.twilio.com/2010-04-01/Accounts/YOUR_ACCOUNT_SID/Messages.json" \
-u "YOUR_ACCOUNT_SID:YOUR_AUTH_TOKEN" \
-d "From=whatsapp:+***********" \
-d "To=whatsapp:+966XXXXXXXXX" \
-d "Body=🔔 تنبيه استحقاق حساب

📋 اسم الحساب: [ACCOUNT_NAME]
💰 المبلغ: [PRICE]
📅 تاريخ الاستحقاق: [DUE_DATE]
⏰ المتبقي: [DAYS] يوم

يرجى تجديد الحساب قبل انتهاء الصلاحية."
```

#### استخدام WhatsApp Business API:
```bash
curl -X POST "https://graph.facebook.com/v17.0/YOUR_PHONE_NUMBER_ID/messages" \
-H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
-H "Content-Type: application/json" \
-d '{
  "messaging_product": "whatsapp",
  "to": "966XXXXXXXXX",
  "type": "text",
  "text": {
    "body": "🔔 تنبيه استحقاق حساب\n\n📋 اسم الحساب: [ACCOUNT_NAME]\n💰 المبلغ: [PRICE]\n📅 تاريخ الاستحقاق: [DUE_DATE]\n⏰ المتبقي: [DAYS] يوم\n\nيرجى تجديد الحساب قبل انتهاء الصلاحية."
  }
}'
```

#### استخدام خدمة محلية (مثل WhatsApp Web API):
```bash
curl -X POST "http://localhost:3000/send-message" \
-H "Content-Type: application/json" \
-d '{
  "phone": "966XXXXXXXXX",
  "message": "🔔 تنبيه استحقاق حساب\n\n📋 اسم الحساب: [ACCOUNT_NAME]\n💰 المبلغ: [PRICE]\n📅 تاريخ الاستحقاق: [DUE_DATE]\n⏰ المتبقي: [DAYS] يوم\n\nيرجى تجديد الحساب قبل انتهاء الصلاحية."
}'
```

## 📁 هيكل المشروع

```
Account Hosting/
├── Models/
│   └── Account.cs                 # نموذج بيانات الحساب
├── ViewModels/
│   ├── BaseViewModel.cs           # ViewModel أساسي
│   └── MainViewModel.cs           # ViewModel الرئيسي
├── Views/
│   ├── AccountDialog.xaml         # نافذة إضافة/تعديل الحساب
│   └── AccountDialog.xaml.cs
├── Services/
│   ├── DataService.cs             # خدمة إدارة البيانات JSON
│   ├── WhatsAppNotificationService.cs  # خدمة إشعارات واتساب
│   └── NotificationSchedulerService.cs # خدمة المراقبة التلقائية
├── Commands/
│   └── RelayCommand.cs            # تنفيذ ICommand
├── Converters/
│   └── BooleanToVisibilityConverter.cs # محول Boolean إلى Visibility
├── MainWindow.xaml                # النافذة الرئيسية
├── MainWindow.xaml.cs
├── App.xaml
├── App.xaml.cs
└── README.md
```

## 💾 تخزين البيانات

البيانات تُحفظ في ملف JSON في المسار:
```
%AppData%/AccountHosting/accounts.json
```

مثال على هيكل البيانات:
```json
[
  {
    "Id": 1,
    "AccountName": "استضافة موقع الشركة",
    "Price": 500.00,
    "CreatedDate": "2024-01-15T10:30:00",
    "DueDate": "2025-01-15T10:30:00",
    "WhatsAppNotificationSent": false
  }
]
```

## 🎨 تخصيص التصميم

يمكن تخصيص ألوان وأنماط التصميم من خلال تعديل الـ Resources في `MainWindow.xaml`:

- **الألوان الأساسية**: `#007ACC` (أزرق)
- **ألوان الحالة**: 
  - نشط: `#28A745` (أخضر)
  - تحذير: `#FFC107` (أصفر)
  - خطر: `#DC3545` (أحمر)
- **الخلفية**: `#F8F9FA` (رمادي فاتح)

## 🔔 آلية الإشعارات

1. **الفحص التلقائي**: كل ساعة يتم فحص جميع الحسابات
2. **شرط الإرسال**: قبل 30 يوم من تاريخ الانتهاء
3. **منع التكرار**: لا يتم إرسال إشعار مرة أخرى للحساب نفسه
4. **الفحص اليدوي**: يمكن الضغط على زر "فحص الإشعارات" للفحص الفوري

## 🛠️ التطوير والمساهمة

المشروع يستخدم نمط MVVM ومكتوب بطريقة نظيفة مع تعليقات شاملة. لإضافة مميزات جديدة:

1. إنشاء فرع جديد
2. إضافة الكود مع التعليقات
3. اختبار الوظائف الجديدة
4. إرسال Pull Request

## 📝 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## 📞 الدعم

لأي استفسارات أو مشاكل، يرجى فتح Issue في المستودع أو التواصل مع فريق التطوير.
