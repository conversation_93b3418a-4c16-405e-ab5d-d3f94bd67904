using System;
using System.IO;
using Newtonsoft.Json;

namespace Account_Hosting.Services
{
    /// <summary>
    /// خدمة إدارة إعدادات التطبيق
    /// </summary>
    public class ConfigurationService
    {
        private readonly string _configFilePath;
        private AppSettings? _settings;

        public ConfigurationService()
        {
            _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
            LoadSettings();
        }

        /// <summary>
        /// إعدادات التطبيق
        /// </summary>
        public AppSettings Settings => _settings ?? new AppSettings();

        /// <summary>
        /// يحمل الإعدادات من ملف JSON
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var json = File.ReadAllText(_configFilePath);
                    _settings = JsonConvert.DeserializeObject<AppSettings>(json) ?? new AppSettings();
                }
                else
                {
                    _settings = new AppSettings();
                    SaveSettings(); // إنشاء ملف الإعدادات الافتراضي
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإعدادات: {ex.Message}");
                _settings = new AppSettings();
            }
        }

        /// <summary>
        /// يحفظ الإعدادات في ملف JSON
        /// </summary>
        public void SaveSettings()
        {
            try
            {
                var json = JsonConvert.SerializeObject(_settings, Formatting.Indented);
                File.WriteAllText(_configFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الإعدادات: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// نموذج إعدادات التطبيق
    /// </summary>
    public class AppSettings
    {
        public WhatsAppSettings WhatsAppSettings { get; set; } = new();
        public ApplicationSettings ApplicationSettings { get; set; } = new();
        public UISettings UISettings { get; set; } = new();
    }

    /// <summary>
    /// إعدادات واتساب
    /// </summary>
    public class WhatsAppSettings
    {
        public string ApiUrl { get; set; } = "https://api.whatsapp.com/send";
        public string PhoneNumber { get; set; } = "+966XXXXXXXXX";
        public string ApiToken { get; set; } = "YOUR_API_TOKEN_HERE";
        public int NotificationDaysBeforeExpiry { get; set; } = 30;
        public int CheckIntervalHours { get; set; } = 1;
    }

    /// <summary>
    /// إعدادات التطبيق العامة
    /// </summary>
    public class ApplicationSettings
    {
        public string DataFileName { get; set; } = "accounts.json";
        public bool AutoStartNotificationService { get; set; } = true;
        public string WindowTitle { get; set; } = "إدارة الحسابات - Account Hosting Manager";
        public string Language { get; set; } = "ar-SA";
    }

    /// <summary>
    /// إعدادات واجهة المستخدم
    /// </summary>
    public class UISettings
    {
        public string Theme { get; set; } = "Flat";
        public string PrimaryColor { get; set; } = "#007ACC";
        public string SecondaryColor { get; set; } = "#28A745";
        public string WarningColor { get; set; } = "#FFC107";
        public string DangerColor { get; set; } = "#DC3545";
        public string BackgroundColor { get; set; } = "#F8F9FA";
    }
}
