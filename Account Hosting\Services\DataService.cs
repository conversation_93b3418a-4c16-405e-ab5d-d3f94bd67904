using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Account_Hosting.Models;

namespace Account_Hosting.Services
{
    /// <summary>
    /// خدمة إدارة البيانات - تتعامل مع حفظ وتحميل البيانات من ملف JSON
    /// </summary>
    public class DataService
    {
        private readonly string _dataFilePath;

        public DataService()
        {
            // إنشاء مجلد البيانات إذا لم يكن موجوداً
            var dataDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "AccountHosting");
            Directory.CreateDirectory(dataDirectory);
            
            _dataFilePath = Path.Combine(dataDirectory, "accounts.json");
        }

        /// <summary>
        /// يحمل قائمة الحسابات من ملف JSON
        /// </summary>
        /// <returns>قائمة الحسابات</returns>
        public async Task<List<Account>> LoadAccountsAsync()
        {
            try
            {
                if (!File.Exists(_dataFilePath))
                {
                    // إنشاء ملف فارغ إذا لم يكن موجوداً
                    await SaveAccountsAsync(new List<Account>());
                    return new List<Account>();
                }

                var json = await File.ReadAllTextAsync(_dataFilePath);
                var accounts = JsonConvert.DeserializeObject<List<Account>>(json) ?? new List<Account>();
                
                return accounts;
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، إرجاع قائمة فارغة
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات: {ex.Message}");
                return new List<Account>();
            }
        }

        /// <summary>
        /// يحفظ قائمة الحسابات في ملف JSON
        /// </summary>
        /// <param name="accounts">قائمة الحسابات</param>
        public async Task SaveAccountsAsync(List<Account> accounts)
        {
            try
            {
                var json = JsonConvert.SerializeObject(accounts, Formatting.Indented);
                await File.WriteAllTextAsync(_dataFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ البيانات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// يحصل على مسار ملف البيانات
        /// </summary>
        public string GetDataFilePath() => _dataFilePath;
    }
}
