using System;
using System.Threading.Tasks;
using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;
using Account_Hosting.Models;

namespace Account_Hosting.Services
{
    /// <summary>
    /// خدمة إرسال إشعارات البريد الإلكتروني
    /// </summary>
    public class EmailNotificationService
    {
        private readonly ConfigurationService _configService;
        private EmailSettings EmailConfig => _configService.Settings.EmailSettings;

        public EmailNotificationService()
        {
            _configService = new ConfigurationService();
        }

        /// <summary>
        /// يرسل إشعار بريد إلكتروني للحساب المحدد
        /// </summary>
        /// <param name="account">الحساب المراد إرسال إشعار له</param>
        /// <returns>true إذا تم الإرسال بنجاح، false إذا فشل</returns>
        public async Task<bool> SendExpirationNotificationAsync(Account account)
        {
            try
            {
                // إنشاء رسالة البريد الإلكتروني
                var message = CreateEmailMessage(account);

                // إرسال الرسالة
                var success = await SendEmailAsync(message);

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"تم إرسال إشعار بريد إلكتروني للحساب: {account.AccountName}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"فشل في إرسال إشعار بريد إلكتروني للحساب: {account.AccountName}");
                }

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إرسال إشعار بريد إلكتروني: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ينشئ رسالة البريد الإلكتروني
        /// </summary>
        /// <param name="account">الحساب</param>
        /// <returns>رسالة البريد الإلكتروني</returns>
        private MimeMessage CreateEmailMessage(Account account)
        {
            var daysUntilExpiry = (account.DueDate - DateTime.Now).Days;

            var message = new MimeMessage();

            // المرسل
            message.From.Add(new MailboxAddress(EmailConfig.SenderName, EmailConfig.SenderEmail));

            // المستقبل
            message.To.Add(new MailboxAddress("", EmailConfig.RecipientEmail));

            // موضوع الرسالة
            message.Subject = $"🔔 تنبيه استحقاق حساب: {account.AccountName}";

            // محتوى الرسالة
            var bodyBuilder = new BodyBuilder();

            // النص العادي
            bodyBuilder.TextBody = CreatePlainTextMessage(account, daysUntilExpiry);

            // النص المنسق (HTML)
            bodyBuilder.HtmlBody = CreateHtmlMessage(account, daysUntilExpiry);

            message.Body = bodyBuilder.ToMessageBody();

            return message;
        }

        /// <summary>
        /// ينشئ نص الرسالة العادي
        /// </summary>
        /// <param name="account">الحساب</param>
        /// <param name="daysUntilExpiry">عدد الأيام المتبقية</param>
        /// <returns>نص الرسالة</returns>
        private string CreatePlainTextMessage(Account account, int daysUntilExpiry)
        {
            return $"تنبيه استحقاق حساب\n\n" +
                   $"اسم الحساب: {account.AccountName}\n" +
                   $"المبلغ: {account.Price:C}\n" +
                   $"تاريخ الإنشاء: {account.CreatedDate:yyyy/MM/dd}\n" +
                   $"تاريخ الاستحقاق: {account.DueDate:yyyy/MM/dd}\n" +
                   $"الأيام المتبقية: {daysUntilExpiry} يوم\n\n" +
                   $"يرجى تجديد الحساب قبل انتهاء الصلاحية.\n\n" +
                   $"تم إرسال هذا التنبيه تلقائياً من نظام إدارة الحسابات\n" +
                   $"التاريخ والوقت: {DateTime.Now:yyyy/MM/dd HH:mm:ss}";
        }

        /// <summary>
        /// ينشئ نص الرسالة المنسق (HTML)
        /// </summary>
        /// <param name="account">الحساب</param>
        /// <param name="daysUntilExpiry">عدد الأيام المتبقية</param>
        /// <returns>نص الرسالة المنسق</returns>
        private string CreateHtmlMessage(Account account, int daysUntilExpiry)
        {
            var statusColor = daysUntilExpiry <= 7 ? "#dc3545" : daysUntilExpiry <= 15 ? "#ffc107" : "#fd7e14";
            var statusIcon = daysUntilExpiry <= 7 ? "🚨" : daysUntilExpiry <= 15 ? "⚠️" : "🔔";

            return $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تنبيه استحقاق حساب</title>
</head>
<body style='font-family: Arial, sans-serif; background-color: #f8f9fa; margin: 0; padding: 20px; direction: rtl;'>
    <div style='max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;'>
        
        <!-- Header -->
        <div style='background: linear-gradient(135deg, #007acc, #0056b3); color: white; padding: 30px; text-align: center;'>
            <h1 style='margin: 0; font-size: 28px; font-weight: bold;'>{statusIcon} تنبيه استحقاق حساب</h1>
            <p style='margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;'>نظام إدارة الحسابات</p>
        </div>
        
        <!-- Content -->
        <div style='padding: 30px;'>
            <div style='background-color: {statusColor}; color: white; padding: 15px; border-radius: 8px; margin-bottom: 25px; text-align: center;'>
                <h2 style='margin: 0; font-size: 20px;'>الحساب يحتاج تجديد خلال {daysUntilExpiry} يوم</h2>
            </div>
            
            <table style='width: 100%; border-collapse: collapse; margin-bottom: 25px;'>
                <tr style='background-color: #f8f9fa;'>
                    <td style='padding: 15px; border: 1px solid #dee2e6; font-weight: bold; width: 30%;'>📋 اسم الحساب:</td>
                    <td style='padding: 15px; border: 1px solid #dee2e6;'>{account.AccountName}</td>
                </tr>
                <tr>
                    <td style='padding: 15px; border: 1px solid #dee2e6; font-weight: bold; background-color: #f8f9fa;'>💰 المبلغ:</td>
                    <td style='padding: 15px; border: 1px solid #dee2e6; color: #28a745; font-weight: bold;'>{account.Price:C}</td>
                </tr>
                <tr style='background-color: #f8f9fa;'>
                    <td style='padding: 15px; border: 1px solid #dee2e6; font-weight: bold;'>📅 تاريخ الإنشاء:</td>
                    <td style='padding: 15px; border: 1px solid #dee2e6;'>{account.CreatedDate:yyyy/MM/dd}</td>
                </tr>
                <tr>
                    <td style='padding: 15px; border: 1px solid #dee2e6; font-weight: bold; background-color: #f8f9fa;'>⏰ تاريخ الاستحقاق:</td>
                    <td style='padding: 15px; border: 1px solid #dee2e6; color: {statusColor}; font-weight: bold;'>{account.DueDate:yyyy/MM/dd}</td>
                </tr>
                <tr style='background-color: #f8f9fa;'>
                    <td style='padding: 15px; border: 1px solid #dee2e6; font-weight: bold;'>⏳ الأيام المتبقية:</td>
                    <td style='padding: 15px; border: 1px solid #dee2e6; color: {statusColor}; font-weight: bold; font-size: 18px;'>{daysUntilExpiry} يوم</td>
                </tr>
            </table>
            
            <div style='background-color: #e7f3ff; border-right: 4px solid #007acc; padding: 20px; border-radius: 5px; margin-bottom: 25px;'>
                <h3 style='margin: 0 0 10px 0; color: #007acc;'>📌 تذكير مهم</h3>
                <p style='margin: 0; line-height: 1.6;'>يرجى تجديد الحساب قبل انتهاء الصلاحية لتجنب انقطاع الخدمة.</p>
            </div>
        </div>
        
        <!-- Footer -->
        <div style='background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;'>
            <p style='margin: 0; color: #6c757d; font-size: 14px;'>
                تم إرسال هذا التنبيه تلقائياً من نظام إدارة الحسابات<br>
                التاريخ والوقت: {DateTime.Now:yyyy/MM/dd HH:mm:ss}
            </p>
        </div>
    </div>
</body>
</html>";
        }

        /// <summary>
        /// يرسل رسالة البريد الإلكتروني
        /// </summary>
        /// <param name="message">رسالة البريد الإلكتروني</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        private async Task<bool> SendEmailAsync(MimeMessage message)
        {
            try
            {
                using var client = new SmtpClient();

                // الاتصال بخادم SMTP
                var secureOptions = EmailConfig.EnableSsl ? SecureSocketOptions.StartTls : SecureSocketOptions.None;
                await client.ConnectAsync(EmailConfig.SmtpServer, EmailConfig.SmtpPort, secureOptions);

                // المصادقة
                await client.AuthenticateAsync(EmailConfig.SenderEmail, EmailConfig.SenderPassword);

                // إرسال الرسالة
                await client.SendAsync(message);

                // قطع الاتصال
                await client.DisconnectAsync(true);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إرسال البريد الإلكتروني: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// يتحقق من الحسابات التي تحتاج إشعار (قبل شهر من الاستحقاق)
        /// </summary>
        /// <param name="account">الحساب</param>
        /// <returns>true إذا كان الحساب يحتاج إشعار</returns>
        public bool ShouldSendNotification(Account account)
        {
            // لا نرسل إشعارات للحسابات المعطلة أو التي تم إرسال إشعار لها بالفعل
            if (!account.IsAccountActive || account.EmailNotificationSent)
                return false;

            var daysUntilExpiry = (account.DueDate - DateTime.Now).Days;
            return daysUntilExpiry <= EmailConfig.NotificationDaysBeforeExpiry && daysUntilExpiry > 0;
        }
    }
}
