using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;

namespace Account_Hosting.Services
{
    /// <summary>
    /// خدمة جدولة الإشعارات التلقائية
    /// </summary>
    public class NotificationSchedulerService : IDisposable
    {
        private readonly DispatcherTimer _timer;
        private readonly DataService _dataService;
        private readonly WhatsAppNotificationService _whatsAppService;
        private bool _disposed = false;

        public NotificationSchedulerService(DataService dataService, WhatsAppNotificationService whatsAppService)
        {
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _whatsAppService = whatsAppService ?? throw new ArgumentNullException(nameof(whatsAppService));

            // إعداد مؤقت للفحص كل ساعة
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromHours(1) // فحص كل ساعة
            };
            _timer.Tick += Timer_Tick;
        }

        /// <summary>
        /// يبدأ خدمة المراقبة التلقائية
        /// </summary>
        public void Start()
        {
            _timer.Start();
            
            // تشغيل فحص فوري عند البدء
            _ = Task.Run(async () => await CheckNotificationsAsync());
        }

        /// <summary>
        /// يوقف خدمة المراقبة التلقائية
        /// </summary>
        public void Stop()
        {
            _timer.Stop();
        }

        /// <summary>
        /// معالج حدث المؤقت
        /// </summary>
        private async void Timer_Tick(object? sender, EventArgs e)
        {
            await CheckNotificationsAsync();
        }

        /// <summary>
        /// يفحص الحسابات ويرسل الإشعارات المطلوبة
        /// </summary>
        private async Task CheckNotificationsAsync()
        {
            try
            {
                var accounts = await _dataService.LoadAccountsAsync();
                var notificationsSent = 0;
                var accountsUpdated = false;

                foreach (var account in accounts)
                {
                    if (_whatsAppService.ShouldSendNotification(account))
                    {
                        var success = await _whatsAppService.SendExpirationNotificationAsync(account);
                        if (success)
                        {
                            account.WhatsAppNotificationSent = true;
                            notificationsSent++;
                            accountsUpdated = true;
                        }
                    }
                }

                // حفظ التحديثات إذا تم إرسال إشعارات
                if (accountsUpdated)
                {
                    await _dataService.SaveAccountsAsync(accounts);
                }

                // تسجيل النتيجة
                if (notificationsSent > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"تم إرسال {notificationsSent} إشعار واتساب تلقائياً");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الفحص التلقائي للإشعارات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحرير الموارد
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _timer?.Stop();
                _disposed = true;
            }
        }
    }
}
