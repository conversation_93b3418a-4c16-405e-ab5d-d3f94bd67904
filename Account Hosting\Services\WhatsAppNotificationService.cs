using System;
using System.Diagnostics;
using System.Threading.Tasks;
using Account_Hosting.Models;

namespace Account_Hosting.Services
{
    /// <summary>
    /// خدمة إرسال إشعارات واتساب
    /// </summary>
    public class WhatsAppNotificationService
    {
        // يمكن تخصيص هذه القيم حسب API المستخدم
        private const string WHATSAPP_API_URL = "https://api.whatsapp.com/send";
        private const string PHONE_NUMBER = "+966XXXXXXXXX"; // رقم الهاتف المستقبل

        /// <summary>
        /// يرسل إشعار واتساب للحساب المحدد
        /// </summary>
        /// <param name="account">الحساب المراد إرسال إشعار له</param>
        /// <returns>true إذا تم الإرسال بنجاح، false إذا فشل</returns>
        public async Task<bool> SendExpirationNotificationAsync(Account account)
        {
            try
            {
                // إنشاء نص الرسالة
                var message = CreateNotificationMessage(account);

                // إرسال الرسالة باستخدام cURL
                var success = await SendWhatsAppMessageAsync(message);

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"تم إرسال إشعار واتساب للحساب: {account.AccountName}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"فشل في إرسال إشعار واتساب للحساب: {account.AccountName}");
                }

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إرسال إشعار واتساب: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ينشئ نص رسالة الإشعار
        /// </summary>
        /// <param name="account">الحساب</param>
        /// <returns>نص الرسالة</returns>
        private string CreateNotificationMessage(Account account)
        {
            var daysUntilExpiry = (account.DueDate - DateTime.Now).Days;

            return $"🔔 تنبيه استحقاق حساب\n\n" +
                   $"📋 اسم الحساب: {account.AccountName}\n" +
                   $"💰 المبلغ: {account.Price:C}\n" +
                   $"📅 تاريخ الاستحقاق: {account.DueDate:yyyy/MM/dd}\n" +
                   $"⏰ المتبقي: {daysUntilExpiry} يوم\n\n" +
                   $"يرجى تجديد الحساب قبل انتهاء الصلاحية.";
        }

        /// <summary>
        /// يرسل رسالة واتساب باستخدام cURL
        /// </summary>
        /// <param name="message">نص الرسالة</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        private async Task<bool> SendWhatsAppMessageAsync(string message)
        {
            try
            {
                // مثال على أمر cURL لإرسال رسالة واتساب
                // يجب تعديل هذا الأمر حسب API المستخدم (مثل Twilio, WhatsApp Business API, إلخ)

                var encodedMessage = Uri.EscapeDataString(message);
                var curlCommand = $"curl -X POST \"{WHATSAPP_API_URL}\" " +
                                $"-H \"Content-Type: application/json\" " +
                                $"-H \"Authorization: Bearer YOUR_API_TOKEN\" " +
                                $"-d \"{{\\\"to\\\":\\\"{PHONE_NUMBER}\\\",\\\"text\\\":\\\"{encodedMessage}\\\"}}\"";

                // تنفيذ أمر cURL
                var processInfo = new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = $"/c {curlCommand}",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(processInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    var output = await process.StandardOutput.ReadToEndAsync();
                    var error = await process.StandardError.ReadToEndAsync();

                    // تحقق من نجاح العملية (يجب تخصيص هذا حسب استجابة API)
                    return process.ExitCode == 0 && !output.Contains("error");
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنفيذ cURL: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// يتحقق من الحسابات التي تحتاج إشعار (قبل شهر من الاستحقاق)
        /// </summary>
        /// <param name="account">الحساب</param>
        /// <returns>true إذا كان الحساب يحتاج إشعار</returns>
        public bool ShouldSendNotification(Account account)
        {
            // لا نرسل إشعارات للحسابات المعطلة
            if (!account.IsAccountActive || account.WhatsAppNotificationSent)
                return false;

            var daysUntilExpiry = (account.DueDate - DateTime.Now).Days;
            return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
        }
    }
}
