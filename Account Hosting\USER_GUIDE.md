# 📖 دليل المستخدم - إدارة الحسابات

## 🚀 البدء السريع

### 1. تشغيل التطبيق

- افتح ملف `Account Hosting.exe`
- ستظهر النافذة الرئيسية مع جدول فارغ في البداية

### 2. إضافة حساب جديد

1. اضغط على زر **"➕ إضافة حساب"**
2. أدخل اسم الحساب (مثل: "استضافة موقع الشركة")
3. أد<PERSON><PERSON> السعر (مثل: 500)
4. اضغط **"حفظ"**

> 📝 **ملاحظة**: تاريخ الإنشاء يُحدد تلقائياً لليوم الحالي، وتاريخ الاستحقاق يُحدد بعد سنة كاملة.

### 3. تعديل حساب موجود

1. اختر الحساب من الجدول
2. اضغط على زر **"✏️ تعديل"**
3. عدّل البيانات المطلوبة
4. اضغط **"حفظ"**

### 4. حذف حساب

1. اختر الحساب من الجدول
2. اضغط على زر **"🗑️ حذف الحساب"**
3. أكد الحذف في النافذة المنبثقة

### 5. تفعيل/تعطيل حساب

1. اختر الحساب من الجدول
2. اضغط على زر **"✏️ تعديل الحساب"**
3. قم بتحديد أو إلغاء تحديد مربع **"الحساب نشط"**
4. اضغط **"حفظ"**

> 📝 **ملاحظة**: الحسابات المعطلة لن تظهر في الإشعارات ولن يتم إرسال تنبيهات واتساب لها.

## 🔔 نظام الإشعارات

### الإشعارات التلقائية

- يفحص التطبيق الحسابات كل ساعة تلقائياً
- يرسل إشعار واتساب قبل **30 يوم** من انتهاء الصلاحية
- لا يتم إرسال إشعار مكرر للحساب نفسه

### الفحص اليدوي

- اضغط على زر **"🔔 فحص الإشعارات"** للفحص الفوري
- ستظهر رسالة في شريط الحالة تخبرك بعدد الإشعارات المرسلة

### حالات الحسابات

| الحالة   | المعنى                     | اللون |
| -------- | -------------------------- | ----- |
| ✅ نشط   | أكثر من 30 يوم متبقي ومفعل | أخضر  |
| ⚠️ قريب  | 30 يوم أو أقل متبقي ومفعل  | أصفر  |
| ❌ منتهي | تجاوز تاريخ الاستحقاق      | أحمر  |
| ⏸️ معطل  | تم تعطيل الحساب يدوياً     | رمادي |

## 🛠️ الإعدادات

### إعداد واتساب

1. افتح ملف `appsettings.json`
2. حدث القيم التالية:
   ```json
   {
     "WhatsAppSettings": {
       "ApiUrl": "رابط API الخاص بك",
       "PhoneNumber": "+966XXXXXXXXX",
       "ApiToken": "رمز API الخاص بك"
     }
   }
   ```

### تخصيص الألوان

يمكنك تغيير ألوان التطبيق من ملف `appsettings.json`:

```json
{
  "UISettings": {
    "PrimaryColor": "#007ACC",
    "SecondaryColor": "#28A745",
    "WarningColor": "#FFC107",
    "DangerColor": "#DC3545"
  }
}
```

## 💾 إدارة البيانات

### موقع ملف البيانات

البيانات محفوظة في:

```
%AppData%\AccountHosting\accounts.json
```

### النسخ الاحتياطي

- انسخ ملف `accounts.json` بانتظام
- يمكنك استعادة البيانات بنسخ الملف إلى نفس المكان

### استيراد بيانات تجريبية

1. انسخ محتوى ملف `sample-data.json`
2. الصقه في ملف `accounts.json`
3. أعد تشغيل التطبيق

## 🔍 البحث والتصفية

### ترتيب الجدول

- اضغط على عنوان أي عمود لترتيب البيانات
- اضغط مرة أخرى للترتيب العكسي

### قراءة شريط الحالة

يعرض شريط الحالة:

- إجمالي عدد الحسابات
- عدد الحسابات التي تنتهي قريباً (خلال 30 يوم)

## ⚠️ استكشاف الأخطاء

### التطبيق لا يبدأ

- تأكد من تثبيت .NET 9.0
- تشغيل التطبيق كمسؤول
- تحقق من وجود ملف `appsettings.json`

### لا تظهر البيانات

- تحقق من وجود ملف البيانات في `%AppData%\AccountHosting\`
- تأكد من صحة تنسيق JSON
- اضغط زر **"🔄 تحديث"**

### الإشعارات لا تعمل

- تحقق من إعدادات واتساب في `appsettings.json`
- تأكد من صحة رمز API
- تحقق من اتصال الإنترنت

### رسائل خطأ شائعة

| الخطأ                   | الحل                    |
| ----------------------- | ----------------------- |
| "خطأ في تحميل البيانات" | تحقق من ملف البيانات    |
| "خطأ في حفظ البيانات"   | تأكد من صلاحيات الكتابة |
| "فشل في إرسال الإشعار"  | تحقق من إعدادات واتساب  |

## 💡 نصائح للاستخدام الأمثل

### 1. التنظيم

- استخدم أسماء واضحة للحسابات
- أضف تفاصيل مفيدة في اسم الحساب
- مثال: "استضافة موقع الشركة - خطة Premium"

### 2. المراقبة

- راجع التطبيق أسبوعياً
- انتبه للحسابات ذات الحالة "⚠️ قريب"
- استخدم زر فحص الإشعارات بانتظام

### 3. النسخ الاحتياطي

- اعمل نسخة احتياطية شهرياً
- احفظ النسخة في مكان آمن
- اختبر استعادة البيانات دورياً

### 4. الأمان

- لا تشارك ملف البيانات مع أشخاص غير مخولين
- احم رمز API الخاص بواتساب
- استخدم كلمات مرور قوية لحساباتك

## 📞 الحصول على المساعدة

### الدعم الفني

- راجع هذا الدليل أولاً
- تحقق من ملف `README.md` للمعلومات التقنية
- ابحث في الأخطاء الشائعة أعلاه

### الإبلاغ عن مشاكل

عند الإبلاغ عن مشكلة، أرفق:

- وصف المشكلة بالتفصيل
- خطوات إعادة إنتاج المشكلة
- لقطة شاشة إن أمكن
- رسالة الخطأ كاملة

### طلب ميزات جديدة

يمكنك اقتراح ميزات جديدة مثل:

- إضافة حقول جديدة للحسابات
- تقارير مفصلة
- تصدير البيانات لـ Excel
- إشعارات بريد إلكتروني

---

**نتمنى لك تجربة ممتعة مع تطبيق إدارة الحسابات! 🎉**
