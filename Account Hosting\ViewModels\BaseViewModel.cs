using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Account_Hosting.ViewModels
{
    /// <summary>
    /// ViewModel أساسي يوفر تنفيذ INotifyPropertyChanged
    /// </summary>
    public abstract class BaseViewModel : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// يثير حدث PropertyChanged للخاصية المحددة
        /// </summary>
        /// <param name="propertyName">اسم الخاصية</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// يحدث قيمة الخاصية ويثير حدث PropertyChanged إذا تغيرت القيمة
        /// </summary>
        /// <typeparam name="T">نوع الخاصية</typeparam>
        /// <param name="field">المرجع للحقل</param>
        /// <param name="value">القيمة الجديدة</param>
        /// <param name="propertyName">اسم الخاصية</param>
        /// <returns>true إذا تغيرت القيمة، false إذا لم تتغير</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
