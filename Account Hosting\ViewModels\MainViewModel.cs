using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Account_Hosting.Commands;
using Account_Hosting.Models;
using Account_Hosting.Services;

namespace Account_Hosting.ViewModels
{
    /// <summary>
    /// ViewModel الرئيسي للنافذة الأساسية
    /// </summary>
    public class MainViewModel : BaseViewModel
    {
        private readonly DataService _dataService;
        private readonly WhatsAppNotificationService _whatsAppService;

        private ObservableCollection<Account> _accounts;
        private Account? _selectedAccount;
        private string _statusMessage;
        private bool _isLoading;

        public MainViewModel()
        {
            _dataService = new DataService();
            _whatsAppService = new WhatsAppNotificationService();
            _accounts = new ObservableCollection<Account>();
            _statusMessage = "جاري التحميل...";

            // تهيئة الأوامر
            AddAccountCommand = new RelayCommand(AddAccount);
            EditAccountCommand = new RelayCommand(EditAccount, () => SelectedAccount != null);
            DeleteAccountCommand = new RelayCommand(DeleteAccount, () => SelectedAccount != null);
            RefreshCommand = new RelayCommand(async () => await LoadAccountsAsync());
            CheckNotificationsCommand = new RelayCommand(async () => await CheckAndSendNotificationsAsync());
            ShowDataLocationCommand = new RelayCommand(ShowDataLocation);

            // تحميل البيانات عند بدء التطبيق
            _ = Task.Run(async () => await LoadAccountsAsync());
        }

        #region Properties

        /// <summary>
        /// قائمة الحسابات
        /// </summary>
        public ObservableCollection<Account> Accounts
        {
            get => _accounts;
            set => SetProperty(ref _accounts, value);
        }

        /// <summary>
        /// الحساب المحدد حالياً
        /// </summary>
        public Account? SelectedAccount
        {
            get => _selectedAccount;
            set
            {
                if (SetProperty(ref _selectedAccount, value))
                {
                    // إعادة تقييم إمكانية تنفيذ الأوامر
                    ((RelayCommand)EditAccountCommand).RaiseCanExecuteChanged();
                    ((RelayCommand)DeleteAccountCommand).RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// رسالة الحالة
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// هل التطبيق في حالة تحميل
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// عدد الحسابات المسجلة
        /// </summary>
        public int AccountsCount => Accounts.Count;

        #endregion

        #region Commands

        public ICommand AddAccountCommand { get; }
        public ICommand EditAccountCommand { get; }
        public ICommand DeleteAccountCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand CheckNotificationsCommand { get; }
        public ICommand ShowDataLocationCommand { get; }

        #endregion

        #region Methods

        /// <summary>
        /// يحمل قائمة الحسابات من ملف البيانات
        /// </summary>
        private async Task LoadAccountsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تحميل البيانات...";

                var accounts = await _dataService.LoadAccountsAsync();

                // تحديث القائمة في UI Thread
                Application.Current.Dispatcher.Invoke(() =>
                {
                    Accounts.Clear();
                    foreach (var account in accounts)
                    {
                        Accounts.Add(account);
                    }

                    UpdateStatusMessage();
                });
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحميل البيانات: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// يحفظ قائمة الحسابات في ملف البيانات
        /// </summary>
        private async Task SaveAccountsAsync()
        {
            try
            {
                await _dataService.SaveAccountsAsync(Accounts.ToList());
                UpdateStatusMessage();
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في حفظ البيانات: {ex.Message}";
            }
        }

        /// <summary>
        /// يضيف حساب جديد
        /// </summary>
        private async void AddAccount()
        {
            var dialog = new Views.AccountDialog();
            if (dialog.ShowDialog() == true && dialog.Account != null)
            {
                // تعيين معرف فريد
                var maxId = Accounts.Any() ? Accounts.Max(a => a.Id) : 0;
                dialog.Account.Id = maxId + 1;

                // تاريخ الاستحقاق يُحسب تلقائياً بناءً على تاريخ الإنشاء المدخل
                dialog.Account.DueDate = dialog.Account.CreatedDate.AddYears(1);

                Accounts.Add(dialog.Account);
                await SaveAccountsAsync();

                OnPropertyChanged(nameof(AccountsCount));
            }
        }

        /// <summary>
        /// يحرر الحساب المحدد
        /// </summary>
        private async void EditAccount()
        {
            if (SelectedAccount == null) return;

            var dialog = new Views.AccountDialog(SelectedAccount);
            if (dialog.ShowDialog() == true && dialog.Account != null)
            {
                // تحديث الحساب في القائمة
                var index = Accounts.IndexOf(SelectedAccount);
                if (index >= 0)
                {
                    Accounts[index] = dialog.Account;
                    await SaveAccountsAsync();
                }
            }
        }

        /// <summary>
        /// يحذف الحساب المحدد
        /// </summary>
        private async void DeleteAccount()
        {
            if (SelectedAccount == null) return;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف الحساب '{SelectedAccount.AccountName}'؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                Accounts.Remove(SelectedAccount);
                await SaveAccountsAsync();

                SelectedAccount = null;
                OnPropertyChanged(nameof(AccountsCount));
            }
        }

        /// <summary>
        /// يتحقق من الحسابات ويرسل الإشعارات المطلوبة
        /// </summary>
        private async Task CheckAndSendNotificationsAsync()
        {
            try
            {
                StatusMessage = "جاري فحص الإشعارات...";

                var notificationsSent = 0;
                foreach (var account in Accounts.Where(a => _whatsAppService.ShouldSendNotification(a)))
                {
                    var success = await _whatsAppService.SendExpirationNotificationAsync(account);
                    if (success)
                    {
                        account.WhatsAppNotificationSent = true;
                        notificationsSent++;
                    }
                }

                if (notificationsSent > 0)
                {
                    await SaveAccountsAsync();
                    StatusMessage = $"تم إرسال {notificationsSent} إشعار واتساب";
                }
                else
                {
                    StatusMessage = "لا توجد إشعارات مطلوبة حالياً";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في فحص الإشعارات: {ex.Message}";
            }
        }

        /// <summary>
        /// يعرض مكان حفظ البيانات
        /// </summary>
        private void ShowDataLocation()
        {
            var dataPath = _dataService.GetDataFilePath();
            var directory = System.IO.Path.GetDirectoryName(dataPath);

            var message = $"مسار ملف البيانات:\n{dataPath}\n\nمجلد البيانات:\n{directory}\n\nهل تريد فتح المجلد؟";

            var result = MessageBox.Show(message, "مكان حفظ البيانات",
                MessageBoxButton.YesNo, MessageBoxImage.Information);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    System.Diagnostics.Process.Start("explorer.exe", directory ?? "");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح المجلد: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// يحدث رسالة الحالة
        /// </summary>
        private void UpdateStatusMessage()
        {
            var activeAccounts = Accounts.Count(a => a.IsAccountActive);
            var disabledAccounts = Accounts.Count(a => !a.IsAccountActive);
            var expiringSoon = Accounts.Count(a => a.IsExpiringSoon);
            var expired = Accounts.Count(a => a.IsExpired);

            StatusMessage = $"إجمالي: {AccountsCount} | نشط: {activeAccounts} | معطل: {disabledAccounts} | ينتهي قريباً: {expiringSoon} | منتهي: {expired}";
        }

        #endregion
    }
}
