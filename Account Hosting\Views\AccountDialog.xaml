<Window x:Class="Account_Hosting.Views.AccountDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الحساب"
        Height="520"
        Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F8F9FA"
        FlowDirection="RightToLeft">

        <Window.Resources>
                <!-- أنماط Flat UI -->
                <Style x:Key="FlatButtonStyle"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="#007ACC"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="25,12"/>
                        <Setter Property="Margin"
                                Value="8"/>
                        <Setter Property="FontSize"
                                Value="15"/>
                        <Setter Property="MinWidth"
                                Value="120"/>
                        <Setter Property="MinHeight"
                                Value="40"/>
                        <Setter Property="FontWeight"
                                Value="SemiBold"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="6">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#005A9E"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#004578"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <Style x:Key="CancelButtonStyle"
                       TargetType="Button"
                       BasedOn="{StaticResource FlatButtonStyle}">
                        <Setter Property="Background"
                                Value="#6C757D"/>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#5A6268"/>
                                </Trigger>
                                <Trigger Property="IsPressed"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#495057"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="FlatTextBoxStyle"
                       TargetType="TextBox">
                        <Setter Property="Padding"
                                Value="10,8"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#DEE2E6"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="Margin"
                                Value="0,5"/>
                        <Style.Triggers>
                                <Trigger Property="IsFocused"
                                         Value="True">
                                        <Setter Property="BorderBrush"
                                                Value="#007ACC"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="LabelStyle"
                       TargetType="Label">
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="FontWeight"
                                Value="SemiBold"/>
                        <Setter Property="Foreground"
                                Value="#495057"/>
                        <Setter Property="Margin"
                                Value="0,10,0,0"/>
                </Style>
        </Window.Resources>

        <Grid Margin="20">
                <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- عنوان النافذة -->
                <TextBlock Grid.Row="0"
                           Text="{Binding Title}"
                           FontSize="18"
                           FontWeight="Bold"
                           Foreground="#212529"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,20"/>

                <!-- اسم الحساب -->
                <Label Grid.Row="1"
                       Content="اسم الحساب:"
                       Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="2"
                         x:Name="AccountNameTextBox"
                         Style="{StaticResource FlatTextBoxStyle}"
                         Text="{Binding AccountName, UpdateSourceTrigger=PropertyChanged}"/>

                <!-- السعر -->
                <Label Grid.Row="3"
                       Content="السعر:"
                       Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="4"
                         x:Name="PriceTextBox"
                         Style="{StaticResource FlatTextBoxStyle}"
                         Text="{Binding Price, UpdateSourceTrigger=PropertyChanged}"/>

                <!-- تاريخ الإنشاء -->
                <Label Grid.Row="5"
                       Content="تاريخ الإنشاء:"
                       Style="{StaticResource LabelStyle}"/>
                <DatePicker Grid.Row="6"
                            x:Name="CreatedDatePicker"
                            Margin="0,5"
                            FontSize="14"
                            SelectedDate="{Binding CreatedDate, UpdateSourceTrigger=PropertyChanged}"
                            FirstDayOfWeek="Saturday"
                            FlowDirection="LeftToRight"/>

                <!-- حالة النشاط -->
                <Label Grid.Row="7"
                       Content="حالة الحساب:"
                       Style="{StaticResource LabelStyle}"/>
                <StackPanel Grid.Row="8"
                            Orientation="Horizontal"
                            Margin="0,5"
                            FlowDirection="RightToLeft">
                        <CheckBox x:Name="IsActiveCheckBox"
                                  Content="الحساب نشط"
                                  IsChecked="{Binding IsAccountActive, UpdateSourceTrigger=PropertyChanged}"
                                  FontSize="14"
                                  FontWeight="SemiBold"
                                  Foreground="#495057"
                                  Margin="5,0"/>
                        <TextBlock Text="(إلغاء التحديد لتعطيل الحساب)"
                                   FontSize="12"
                                   Foreground="#6C757D"
                                   VerticalAlignment="Center"
                                   Margin="10,0"/>
                </StackPanel>

                <!-- الأزرار -->
                <StackPanel Grid.Row="10"
                            Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            Margin="0,20,0,0"
                            FlowDirection="RightToLeft">
                        <Button Content="حفظ"
                                Style="{StaticResource FlatButtonStyle}"
                                Click="SaveButton_Click"
                                IsDefault="True"/>
                        <Button Content="إلغاء"
                                Style="{StaticResource CancelButtonStyle}"
                                Click="CancelButton_Click"
                                IsCancel="True"/>
                </StackPanel>
        </Grid>
</Window>
