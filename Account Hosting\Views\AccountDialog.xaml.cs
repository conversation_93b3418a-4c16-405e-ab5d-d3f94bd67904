using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using Account_Hosting.Models;

namespace Account_Hosting.Views
{
    /// <summary>
    /// نافذة حوار إضافة/تعديل الحسابات
    /// </summary>
    public partial class AccountDialog : Window, INotifyPropertyChanged
    {
        private Account? _account;
        private string _title = string.Empty;
        private string _accountName = string.Empty;
        private decimal _price;
        private DateTime _createdDate = DateTime.Now;
        private bool _isAccountActive = true;

        public AccountDialog() : this(null)
        {
        }

        public AccountDialog(Account? existingAccount)
        {
            InitializeComponent();
            DataContext = this;

            if (existingAccount != null)
            {
                // وضع التعديل
                _account = new Account
                {
                    Id = existingAccount.Id,
                    AccountName = existingAccount.AccountName,
                    Price = existingAccount.Price,
                    CreatedDate = existingAccount.CreatedDate,
                    DueDate = existingAccount.DueDate,
                    WhatsAppNotificationSent = existingAccount.WhatsAppNotificationSent
                };

                Title = "تعديل الحساب";
                AccountName = existingAccount.AccountName;
                Price = existingAccount.Price;
                CreatedDate = existingAccount.CreatedDate;
                IsAccountActive = existingAccount.IsAccountActive;
            }
            else
            {
                // وضع الإضافة
                _account = new Account();
                Title = "إضافة حساب جديد";
            }

            // تركيز على حقل اسم الحساب
            Loaded += (s, e) => AccountNameTextBox.Focus();
        }

        #region Properties

        /// <summary>
        /// عنوان النافذة
        /// </summary>
        public new string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        /// <summary>
        /// اسم الحساب
        /// </summary>
        public string AccountName
        {
            get => _accountName;
            set => SetProperty(ref _accountName, value);
        }

        /// <summary>
        /// سعر الحساب
        /// </summary>
        public decimal Price
        {
            get => _price;
            set => SetProperty(ref _price, value);
        }

        /// <summary>
        /// تاريخ إنشاء الحساب
        /// </summary>
        public DateTime CreatedDate
        {
            get => _createdDate;
            set => SetProperty(ref _createdDate, value);
        }

        /// <summary>
        /// هل الحساب نشط
        /// </summary>
        public bool IsAccountActive
        {
            get => _isAccountActive;
            set => SetProperty(ref _isAccountActive, value);
        }

        /// <summary>
        /// الحساب النتيجة (null إذا تم الإلغاء)
        /// </summary>
        public Account? Account => _account;

        #endregion

        #region Events

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// معالج حدث النقر على زر الحفظ
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من صحة البيانات
            if (string.IsNullOrWhiteSpace(AccountName))
            {
                MessageBox.Show("يرجى إدخال اسم الحساب", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                AccountNameTextBox.Focus();
                return;
            }

            if (Price <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر صحيح أكبر من صفر", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                PriceTextBox.Focus();
                return;
            }

            // تحديث بيانات الحساب
            if (_account != null)
            {
                _account.AccountName = AccountName.Trim();
                _account.Price = Price;
                _account.CreatedDate = CreatedDate;
                _account.IsAccountActive = IsAccountActive;

                // تحديث تاريخ الاستحقاق بناءً على تاريخ الإنشاء
                _account.DueDate = _account.CreatedDate.AddYears(1);
            }

            DialogResult = true;
            Close();
        }

        /// <summary>
        /// معالج حدث النقر على زر الإلغاء
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            _account = null;
            DialogResult = false;
            Close();
        }

        #endregion
    }
}
