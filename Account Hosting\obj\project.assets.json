{"version": 3, "targets": {"net9.0-windows7.0": {"Microsoft.NET.ILLink.Tasks/9.0.2": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}}, "net9.0-windows7.0/win-x64": {"Microsoft.NET.ILLink.Tasks/9.0.2": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}}}, "libraries": {"Microsoft.NET.ILLink.Tasks/9.0.2": {"sha512": "+KFnCLVPicEq99ko0tq+ycTvNLXHw0tImmTZjPloB/DOFLPT56KLfk5aS7wbgXRPzYhXTTBYLGaABea5mke77w==", "type": "package", "path": "microsoft.net.illink.tasks/9.0.2", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "Sdk/Sdk.props", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.9.0.2.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/ILLink.Tasks.dll.config", "tools/net472/Mono.Cecil.Mdb.dll", "tools/net472/Mono.Cecil.Pdb.dll", "tools/net472/Mono.Cecil.Rocks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/Sdk/Sdk.props", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net472/build/Microsoft.NET.ILLink.Tasks.props", "tools/net472/build/Microsoft.NET.ILLink.targets", "tools/net9.0/ILLink.Tasks.deps.json", "tools/net9.0/ILLink.Tasks.dll", "tools/net9.0/Mono.Cecil.Mdb.dll", "tools/net9.0/Mono.Cecil.Pdb.dll", "tools/net9.0/Mono.Cecil.Rocks.dll", "tools/net9.0/Mono.Cecil.dll", "tools/net9.0/Sdk/Sdk.props", "tools/net9.0/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net9.0/build/Microsoft.NET.ILLink.Tasks.props", "tools/net9.0/build/Microsoft.NET.ILLink.targets", "tools/net9.0/illink.deps.json", "tools/net9.0/illink.dll", "tools/net9.0/illink.runtimeconfig.json", "useSharedDesignerContext.txt"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["Microsoft.NET.ILLink.Tasks >= 9.0.2", "Newtonsoft.Json >= 13.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "C:\\Program Files (x86)\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\Account Hosting\\Account Hosting.csproj", "projectName": "Account Hosting", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\Account Hosting\\Account Hosting.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Account Hosting\\Account Hosting\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.2, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.2, 9.0.2]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.2, 9.0.2]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.2, 9.0.2]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}